---
type: "always_apply"
---

# 视频溯源脚本任务

## 任务描述
创建一个视频溯源脚本，将 `best_videos` 目录中的最佳视频追溯到 `cluster_video` 目录中的原始视频文件。

## 任务步骤
[ ] 分析 best_videos 目录结构
[ ] 分析 cluster_video 目录结构
[ ] 设计溯源算法（MD5、文件大小、视频时长等）
[ ] 编写 Python 溯源脚本
[ ] 测试脚本功能
[ ] 生成 best.csv 报告文件

## 输出要求
- CSV 文件包含：best_video_name, original_video_name, cluster_group, match_method, confidence
- 处理异常情况（找不到匹配、多个匹配等）
- 提供进度显示和日志输出

## 进度记录
开始分析目录结构...
