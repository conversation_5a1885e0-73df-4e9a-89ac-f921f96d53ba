---
type: "always_apply"
---

# 视频溯源脚本任务

## 任务描述
创建一个视频溯源脚本，将 `best_videos` 目录中的最佳视频追溯到 `cluster_video` 目录中的原始视频文件。

## 任务步骤
[X] 分析 best_videos 目录结构
[X] 分析 cluster_video 目录结构
[X] 设计溯源算法（MD5、文件大小、视频时长等）
[X] 编写 Python 溯源脚本
[X] 测试脚本功能
[X] 生成 best.csv 报告文件

## 初步结果
- 总视频数: 351
- 成功匹配: 138 (39.3%)
- 无匹配: 213 (60.7%)
- 所有成功匹配都是通过MD5哈希值完全匹配

## 需要改进
- 匹配率偏低，需要分析无匹配的原因
- 可能需要更灵活的文件名匹配策略

## 分析结果
### best_videos 目录结构
- 包含以 "_BEST.mp4" 结尾的视频文件
- 文件名格式：frame{N}_from_{ID}_{dataset}_train_{description}_BEST.mp4
- 例如：frame0_from_02761_NAT2021_train_0428person3_2_0_BEST.mp4

### cluster_video 目录结构
- 包含对应的原始视频文件，按集群组织
- 文件名格式：frame{N}_from_{ID}_{dataset}_train_{description}_{cluster_num}_{variant}_seed{seed}.mp4
- 例如：frame0_from_02761_NAT2021_train_0428person3_2_0_1_seed1.mp4

### 匹配策略
1. 去掉 "_BEST" 后缀得到基础名称
2. 在 cluster_video 中查找以该基础名称开头的文件
3. 使用多种方法验证匹配：MD5、文件大小、视频时长

## 输出要求
- CSV 文件包含：best_video_name, original_video_name, cluster_group, match_method, confidence
- 处理异常情况（找不到匹配、多个匹配等）
- 提供进度显示和日志输出

## 进度记录
[X] 分析 best_videos 目录结构 - 包含带 _BEST 后缀的视频文件
[X] 分析 cluster_video 目录结构 - 包含多个变体的原始视频文件
[X] 设计溯源算法（MD5、文件大小、视频时长等）
[X] 编写 Python 溯源脚本
[X] 测试脚本功能
[X] 生成 best.csv 报告文件

## 目录结构分析
- best_videos: 包含以 _BEST.mp4 结尾的视频文件
- cluster_video: 包含原始视频的多个变体，文件名模式为 frameX_from_XXXXX_..._seed*.mp4 或 frameX_from_XXXXX_..._*_seed*.mp4

## 匹配策略
1. 去掉 _BEST 后缀获取基础名称
2. 在 cluster_video 中查找匹配的文件名前缀
3. 使用文件大小、MD5等方法进行精确匹配
