#!/usr/bin/env python3
"""
测试视频溯源脚本的逻辑
"""

import os
from pathlib import Path
from video_tracing import VideoTracer

def test_extract_base_name():
    """测试基础名称提取"""
    tracer = VideoTracer(".", ".")
    
    test_cases = [
        ("frame0_from_02761_NAT2021_train_0428person3_2_0_BEST.mp4", "frame0_from_02761_NAT2021_train_0428person3_2_0"),
        ("frame100_from_00143_urbanvideo_train_BEST.mp4", "frame100_from_00143_urbanvideo_train"),
        ("frame101_from_01589_NAT2021_train_0000person6_1_0_BEST.mp4", "frame101_from_01589_NAT2021_train_0000person6_1_0")
    ]
    
    print("=== 测试基础名称提取 ===")
    for input_name, expected in test_cases:
        result = tracer.extract_base_name(input_name)
        status = "✓" if result == expected else "✗"
        print(f"{status} {input_name} -> {result}")
        if result != expected:
            print(f"  期望: {expected}")

def test_extract_cluster_group():
    """测试集群组别提取"""
    tracer = VideoTracer(".", ".")
    
    test_cases = [
        ("frame0_from_02761_NAT2021_train_0428person3_2_0_1_seed1.mp4", "1"),
        ("frame100_from_00143_urbanvideo_train_1_results_seed1.mp4", "1"),
        ("frame101_from_01589_NAT2021_train_0000person6_1_0_2_seed42.mp4", "2"),
        ("frame0_from_04991_WebUAV3M_train_harvester_41_2_3_seed42.mp4", "3"),
        ("frame101_from_05142_WebUAV3M_train_hatchback_75_1_8_seed42.mp4", "8")
    ]
    
    print("\n=== 测试集群组别提取 ===")
    for input_name, expected in test_cases:
        result = tracer.extract_cluster_group(input_name)
        status = "✓" if result == expected else "✗"
        print(f"{status} {input_name} -> {result}")
        if result != expected:
            print(f"  期望: {expected}")

def test_find_candidates():
    """测试候选文件查找"""
    best_videos_dir = "/data0/trz/ms-swift_SFT/video-storage/ALL/best_videos"
    cluster_videos_dir = "/data0/trz/ms-swift_SFT/video-storage/ALL/cluster_video"

    tracer = VideoTracer(best_videos_dir, cluster_videos_dir)

    # 测试几个实际的文件，包括之前无匹配的
    test_cases = [
        "frame0_from_02761_NAT2021_train_0428person3_2_0",  # 之前无匹配
        "frame100_from_00143_urbanvideo_train",  # 之前有匹配
        "frame0_from_04991_WebUAV3M_train_harvester_41_2",  # 之前有匹配
        "frame101_from_00757_urbanvideo_train",  # 之前无匹配
    ]

    print("\n=== 测试候选文件查找（改进版） ===")
    for base_name in test_cases:
        candidates = tracer.find_candidate_files(base_name)
        print(f"基础名称: {base_name}")
        print(f"找到候选文件: {len(candidates)}")
        if candidates:
            for i, candidate in enumerate(candidates[:5]):  # 显示前5个
                print(f"  {i+1}. {candidate.name}")
            if len(candidates) > 5:
                print(f"  ... 还有 {len(candidates) - 5} 个文件")
        print()

if __name__ == "__main__":
    test_extract_base_name()
    test_extract_cluster_group()
    test_find_candidates()
