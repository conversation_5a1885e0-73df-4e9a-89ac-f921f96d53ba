#!/usr/bin/env python3
"""
分析视频溯源结果
"""

import pandas as pd
from pathlib import Path
from video_tracing import VideoTracer

def analyze_failed_matches():
    """分析匹配失败的情况"""
    df = pd.read_csv('best.csv')
    failed = df[df.match_method == 'failed']
    
    best_videos_dir = "/data0/trz/ms-swift_SFT/video-storage/ALL/best_videos"
    cluster_videos_dir = "/data0/trz/ms-swift_SFT/video-storage/ALL/cluster_video"
    tracer = VideoTracer(best_videos_dir, cluster_videos_dir)
    
    print("=== 分析匹配失败的情况 ===")
    print(f"匹配失败数量: {len(failed)}")
    
    for i, row in failed.head(3).iterrows():
        best_name = row['best_video_name']
        base_name = tracer.extract_base_name(best_name)
        candidates = tracer.find_candidate_files(base_name)
        
        print(f"\n视频: {best_name}")
        print(f"基础名称: {base_name}")
        print(f"候选文件数: {len(candidates)}")
        
        if candidates:
            best_path = Path(best_videos_dir) / best_name
            best_size = tracer.get_file_size(best_path)
            
            print(f"Best视频大小: {best_size} bytes")
            print("候选文件信息:")
            
            for j, candidate in enumerate(candidates[:3]):
                candidate_size = tracer.get_file_size(candidate)
                print(f"  {j+1}. {candidate.name}")
                print(f"     大小: {candidate_size} bytes")
                print(f"     大小匹配: {'是' if candidate_size == best_size else '否'}")

def analyze_no_match():
    """分析完全无匹配的情况"""
    df = pd.read_csv('best.csv')
    no_match = df[df.match_method == 'none']
    
    print(f"\n=== 分析完全无匹配的情况 ===")
    print(f"无匹配数量: {len(no_match)}")
    
    # 按数据集分组统计
    no_match_names = no_match['best_video_name'].tolist()
    
    urbanvideo_count = len([name for name in no_match_names if 'urbanvideo' in name])
    nat2021_count = len([name for name in no_match_names if 'NAT2021' in name])
    webuav3m_count = len([name for name in no_match_names if 'WebUAV3M' in name])
    
    print(f"urbanvideo: {urbanvideo_count}")
    print(f"NAT2021: {nat2021_count}")
    print(f"WebUAV3M: {webuav3m_count}")

def generate_summary():
    """生成总结报告"""
    df = pd.read_csv('best.csv')
    
    print(f"\n=== 视频溯源总结报告 ===")
    print(f"总视频数: {len(df)}")
    print(f"成功匹配: {len(df[df.confidence > 0])} ({len(df[df.confidence > 0])/len(df)*100:.1f}%)")
    print(f"匹配失败: {len(df[df.match_method == 'failed'])} ({len(df[df.match_method == 'failed'])/len(df)*100:.1f}%)")
    print(f"完全无匹配: {len(df[df.match_method == 'none'])} ({len(df[df.match_method == 'none'])/len(df)*100:.1f}%)")
    
    print(f"\n=== 成功匹配的集群分布 ===")
    successful = df[df.confidence > 0]
    cluster_stats = successful['cluster_group'].value_counts()
    print(cluster_stats.head(10))
    
    print(f"\n=== 匹配方法统计 ===")
    print(df['match_method'].value_counts())

if __name__ == "__main__":
    analyze_failed_matches()
    analyze_no_match()
    generate_summary()
