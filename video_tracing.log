2025-07-31 21:05:49,449 - INFO - 开始处理 351 个最佳视频文件
2025-07-31 21:05:49,488 - WARNING - 未找到候选文件: frame0_from_02761_NAT2021_train_0428person3_2_0_BEST.mp4
2025-07-31 21:05:49,534 - INFO - 匹配成功: frame0_from_04991_WebUAV3M_train_harvester_41_2_BEST.mp4 -> frame0_from_04991_WebUAV3M_train_harvester_41_2_4_seed1.mp4
2025-07-31 21:05:49,564 - WARNING - 未找到候选文件: frame0_from_05981_WebUAV3M_train_office_building_15_3_BEST.mp4
2025-07-31 21:05:49,606 - INFO - 匹配成功: frame100_from_00143_urbanvideo_train_BEST.mp4 -> frame100_from_00143_urbanvideo_train_3_results_seed42.mp4
2025-07-31 21:05:49,640 - INFO - 匹配成功: frame100_from_02217_NAT2021_train_0404person2_8_0_BEST.mp4 -> frame100_from_02217_NAT2021_train_0404person2_8_0_4_results_seed1.mp4
2025-07-31 21:05:49,682 - INFO - 匹配成功: frame100_from_03809_WebUAV3M_train_carrying_25_1_BEST.mp4 -> frame100_from_03809_WebUAV3M_train_carrying_25_1_8_seed42.mp4
2025-07-31 21:05:49,710 - WARNING - 未找到候选文件: frame100_from_03973_WebUAV3M_train_chimney_17_1_BEST.mp4
2025-07-31 21:05:49,739 - WARNING - 未找到候选文件: frame100_from_04541_WebUAV3M_train_ferris_wheel_2_3_BEST.mp4
2025-07-31 21:05:49,771 - INFO - 匹配成功: frame101_from_00605_urbanvideo_train_BEST.mp4 -> frame101_from_00605_urbanvideo_train_1_results_seed1.mp4
2025-07-31 21:05:49,799 - WARNING - 未找到候选文件: frame101_from_00757_urbanvideo_train_BEST.mp4
2025-07-31 21:05:49,828 - WARNING - 未找到候选文件: frame101_from_01589_NAT2021_train_0000person6_1_0_BEST.mp4
2025-07-31 21:05:49,857 - INFO - 匹配成功: frame101_from_02574_NAT2021_train_0425car1_4_0_BEST.mp4 -> frame101_from_02574_NAT2021_train_0425car1_4_0_1_results_seed1.mp4
2025-07-31 21:05:49,893 - INFO - 匹配成功: frame101_from_02832_WebUAV3M_train_articulated_bus_5_0_BEST.mp4 -> frame101_from_02832_WebUAV3M_train_articulated_bus_5_0_2_results_seed42.mp4
2025-07-31 21:05:49,922 - WARNING - 未找到候选文件: frame101_from_04616_WebUAV3M_train_ferry_4_0_BEST.mp4
2025-07-31 21:05:49,953 - INFO - 匹配成功: frame101_from_05142_WebUAV3M_train_hatchback_75_1_BEST.mp4 -> frame101_from_05142_WebUAV3M_train_hatchback_75_1_1_seed1.mp4
2025-07-31 21:05:49,981 - WARNING - 未找到候选文件: frame101_from_07282_WebUAV3M_train_riding_an_electric_bicycle_36_0_BEST.mp4
2025-07-31 21:05:50,011 - WARNING - 未找到候选文件: frame102_from_00098_urbanvideo_train_BEST.mp4
2025-07-31 21:05:50,051 - INFO - 匹配成功: frame102_from_00305_urbanvideo_train_BEST.mp4 -> frame102_from_00305_urbanvideo_train_3_results_seed1.mp4
2025-07-31 21:05:50,096 - INFO - 匹配成功: frame102_from_04576_WebUAV3M_train_ferry_12_0_BEST.mp4 -> frame102_from_04576_WebUAV3M_train_ferry_12_0_8_results_seed1.mp4
2025-07-31 21:05:50,128 - INFO - 匹配成功: frame103_from_01678_NAT2021_train_0130car3_2_0_BEST.mp4 -> frame103_from_01678_NAT2021_train_0130car3_2_0_3_results_seed1.mp4
2025-07-31 21:05:50,156 - WARNING - 未找到候选文件: frame103_from_03805_WebUAV3M_train_carrying_24_0_BEST.mp4
2025-07-31 21:05:50,193 - INFO - 匹配成功: frame103_from_04636_WebUAV3M_train_fishing_boat_1_2_BEST.mp4 -> frame103_from_04636_WebUAV3M_train_fishing_boat_1_2_5_results_seed1.mp4
2025-07-31 21:05:50,240 - INFO - 匹配成功: frame103_from_06295_WebUAV3M_train_pagoda_28_4_BEST.mp4 -> frame103_from_06295_WebUAV3M_train_pagoda_28_4_6_results_seed42.mp4
2025-07-31 21:05:50,268 - WARNING - 未找到候选文件: frame103_from_07116_WebUAV3M_train_rapid_transit_4_1_BEST.mp4
2025-07-31 21:05:50,308 - INFO - 匹配成功: frame104_from_00055_urbanvideo_train_BEST.mp4 -> frame104_from_00055_urbanvideo_train_6_seed42.mp4
2025-07-31 21:05:50,337 - WARNING - 未找到候选文件: frame104_from_00604_urbanvideo_train_BEST.mp4
2025-07-31 21:05:50,368 - INFO - 匹配成功: frame104_from_00695_urbanvideo_train_BEST.mp4 -> frame104_from_00695_urbanvideo_train_2_seed42.mp4
2025-07-31 21:05:50,396 - WARNING - 未找到候选文件: frame104_from_03901_WebUAV3M_train_catamaran_3_1_BEST.mp4
2025-07-31 21:05:50,426 - INFO - 匹配成功: frame104_from_04175_WebUAV3M_train_container_ship_3_2_BEST.mp4 -> frame104_from_04175_WebUAV3M_train_container_ship_3_2_1_results_seed42.mp4
2025-07-31 21:05:50,462 - INFO - 匹配成功: frame104_from_06211_WebUAV3M_train_pagoda_10_5_BEST.mp4 -> frame104_from_06211_WebUAV3M_train_pagoda_10_5_5_seed1.mp4
2025-07-31 21:05:50,515 - INFO - 匹配成功: frame105_from_02824_WebUAV3M_train_apartment_8_0_BEST.mp4 -> frame105_from_02824_WebUAV3M_train_apartment_8_0_7_results_seed42.mp4
2025-07-31 21:05:50,554 - INFO - 匹配成功: frame105_from_04388_WebUAV3M_train_dog_4_1_BEST.mp4 -> frame105_from_04388_WebUAV3M_train_dog_4_1_4_seed1.mp4
2025-07-31 21:05:50,582 - WARNING - 未找到候选文件: frame106_from_04916_WebUAV3M_train_great_wall_of_china_7_1_BEST.mp4
2025-07-31 21:05:50,612 - WARNING - 未找到候选文件: frame106_from_07206_WebUAV3M_train_reading_6_1_BEST.mp4
2025-07-31 21:05:50,640 - WARNING - 未找到候选文件: frame106_from_07210_WebUAV3M_train_reading_7_1_BEST.mp4
2025-07-31 21:05:50,669 - WARNING - 未找到候选文件: frame106_from_07311_WebUAV3M_train_riding_an_electric_bicycle_57_0_BEST.mp4
2025-07-31 21:05:50,697 - WARNING - 未找到候选文件: frame107_from_02461_NAT2021_train_0421car3_2_0_BEST.mp4
2025-07-31 21:05:50,725 - WARNING - 未找到候选文件: frame107_from_04792_WebUAV3M_train_going_down_stairs_3_1_BEST.mp4
2025-07-31 21:05:50,754 - WARNING - 未找到候选文件: frame107_from_06119_WebUAV3M_train_office_building_68_1_BEST.mp4
2025-07-31 21:05:50,782 - WARNING - 未找到候选文件: frame108_from_02000_NAT2021_train_0184car1_0_BEST.mp4
2025-07-31 21:05:50,811 - WARNING - 未找到候选文件: frame108_from_03589_WebUAV3M_train_bulk_carrier_40_0_BEST.mp4
2025-07-31 21:05:50,842 - INFO - 匹配成功: frame108_from_03642_WebUAV3M_train_cable_car_3_0_BEST.mp4 -> frame108_from_03642_WebUAV3M_train_cable_car_3_0_1_seed1.mp4
2025-07-31 21:05:50,870 - WARNING - 未找到候选文件: frame108_from_07070_WebUAV3M_train_racing_dinghy_9_0_BEST.mp4
2025-07-31 21:05:50,909 - INFO - 匹配成功: frame109_from_00666_urbanvideo_train_BEST.mp4 -> frame109_from_00666_urbanvideo_train_3_results_seed42.mp4
2025-07-31 21:05:50,940 - INFO - 匹配成功: frame109_from_00745_urbanvideo_train_BEST.mp4 -> frame109_from_00745_urbanvideo_train_4_results_seed1.mp4
2025-07-31 21:05:50,968 - WARNING - 未找到候选文件: frame109_from_01453_NAT2021_train_0000car19_4_0_BEST.mp4
2025-07-31 21:05:50,997 - WARNING - 未找到候选文件: frame109_from_01593_NAT2021_train_0000person6_13_0_BEST.mp4
2025-07-31 21:05:51,027 - WARNING - 未找到候选文件: frame109_from_03730_WebUAV3M_train_calling_8_2_BEST.mp4
2025-07-31 21:05:51,055 - WARNING - 未找到候选文件: frame10_from_01807_NAT2021_train_0138car13_6_0_BEST.mp4
2025-07-31 21:05:51,084 - WARNING - 未找到候选文件: frame110_from_01790_NAT2021_train_0137person1_4_0_BEST.mp4
2025-07-31 21:05:51,112 - WARNING - 未找到候选文件: frame110_from_01852_NAT2021_train_0138person17_4_0_BEST.mp4
2025-07-31 21:05:51,140 - WARNING - 未找到候选文件: frame110_from_04186_WebUAV3M_train_container_ship_9_1_BEST.mp4
2025-07-31 21:05:51,170 - WARNING - 未找到候选文件: frame111_from_02183_NAT2021_train_0404motor1_17_0_BEST.mp4
2025-07-31 21:05:51,219 - INFO - 匹配成功: frame111_from_02936_WebUAV3M_train_beacon_26_1_BEST.mp4 -> frame111_from_02936_WebUAV3M_train_beacon_26_1_8_results_seed42.mp4
2025-07-31 21:05:51,255 - INFO - 匹配成功: frame112_from_02684_NAT2021_train_0427runner2_3_0_BEST.mp4 -> frame112_from_02684_NAT2021_train_0427runner2_3_0_4_results_seed1.mp4
2025-07-31 21:05:51,289 - INFO - 匹配成功: frame112_from_04551_WebUAV3M_train_ferris_wheel_6_0_BEST.mp4 -> frame112_from_04551_WebUAV3M_train_ferris_wheel_6_0_2_seed1.mp4
2025-07-31 21:05:51,321 - INFO - 匹配成功: frame113_from_02460_NAT2021_train_0421car3_15_0_BEST.mp4 -> frame113_from_02460_NAT2021_train_0421car3_15_0_4_results_seed42.mp4
2025-07-31 21:05:51,351 - INFO - 匹配成功: frame114_from_02509_NAT2021_train_0421truck1_3_0_BEST.mp4 -> frame114_from_02509_NAT2021_train_0421truck1_3_0_3_results_seed42.mp4
2025-07-31 21:05:51,380 - WARNING - 未找到候选文件: frame114_from_06228_WebUAV3M_train_pagoda_16_2_BEST.mp4
2025-07-31 21:05:51,409 - WARNING - 未找到候选文件: frame114_from_07023_WebUAV3M_train_pushing_a_cart_6_3_BEST.mp4
2025-07-31 21:05:51,438 - WARNING - 未找到候选文件: frame115_from_00074_urbanvideo_train_BEST.mp4
2025-07-31 21:05:51,465 - WARNING - 未找到候选文件: frame115_from_01551_NAT2021_train_0000car9_5_0_BEST.mp4
2025-07-31 21:05:51,493 - WARNING - 未找到候选文件: frame115_from_03848_WebUAV3M_train_carrying_31_1_BEST.mp4
2025-07-31 21:05:51,521 - WARNING - 未找到候选文件: frame115_from_03965_WebUAV3M_train_chimney_13_6_BEST.mp4
2025-07-31 21:05:51,549 - WARNING - 未找到候选文件: frame115_from_04249_WebUAV3M_train_cow_7_1_BEST.mp4
2025-07-31 21:05:51,576 - WARNING - 未找到候选文件: frame116_from_00515_urbanvideo_train_BEST.mp4
2025-07-31 21:05:51,607 - INFO - 匹配成功: frame116_from_03645_WebUAV3M_train_cable_car_6_1_BEST.mp4 -> frame116_from_03645_WebUAV3M_train_cable_car_6_1_3_seed42.mp4
2025-07-31 21:05:51,636 - WARNING - 未找到候选文件: frame116_from_04194_WebUAV3M_train_container_truck_10_0_BEST.mp4
2025-07-31 21:05:51,664 - WARNING - 未找到候选文件: frame116_from_05993_WebUAV3M_train_office_building_19_2_BEST.mp4
2025-07-31 21:05:51,692 - WARNING - 未找到候选文件: frame116_from_06015_WebUAV3M_train_office_building_28_0_BEST.mp4
2025-07-31 21:05:51,720 - WARNING - 未找到候选文件: frame117_from_00550_urbanvideo_train_BEST.mp4
2025-07-31 21:05:51,748 - WARNING - 未找到候选文件: frame117_from_01958_NAT2021_train_0173building3_5_0_BEST.mp4
2025-07-31 21:05:51,779 - INFO - 匹配成功: frame117_from_06028_WebUAV3M_train_office_building_32_2_BEST.mp4 -> frame117_from_06028_WebUAV3M_train_office_building_32_2_1_seed42.mp4
2025-07-31 21:05:51,813 - INFO - 匹配成功: frame117_from_07043_WebUAV3M_train_pylon_1_4_BEST.mp4 -> frame117_from_07043_WebUAV3M_train_pylon_1_4_4_seed1.mp4
2025-07-31 21:05:51,845 - INFO - 匹配成功: frame118_from_02530_NAT2021_train_0421truck2_6_0_BEST.mp4 -> frame118_from_02530_NAT2021_train_0421truck2_6_0_4_results_seed42.mp4
2025-07-31 21:05:51,873 - WARNING - 未找到候选文件: frame118_from_04965_WebUAV3M_train_harvester_38_3_BEST.mp4
2025-07-31 21:05:51,902 - INFO - 匹配成功: frame118_from_05968_WebUAV3M_train_office_building_10_0_BEST.mp4 -> frame118_from_05968_WebUAV3M_train_office_building_10_0_1_results_seed42.mp4
2025-07-31 21:05:51,931 - INFO - 匹配成功: frame118_from_07271_WebUAV3M_train_riding_an_electric_bicycle_29_0_BEST.mp4 -> frame118_from_07271_WebUAV3M_train_riding_an_electric_bicycle_29_0_1_seed1.mp4
2025-07-31 21:05:51,959 - WARNING - 未找到候选文件: frame118_from_07277_WebUAV3M_train_riding_an_electric_bicycle_32_0_BEST.mp4
2025-07-31 21:05:51,988 - WARNING - 未找到候选文件: frame119_from_01894_NAT2021_train_0138person5_5_0_BEST.mp4
2025-07-31 21:05:52,016 - WARNING - 未找到候选文件: frame119_from_02523_NAT2021_train_0421truck2_20_0_BEST.mp4
2025-07-31 21:05:52,043 - WARNING - 未找到候选文件: frame119_from_03610_WebUAV3M_train_bulk_carrier_5_1_BEST.mp4
2025-07-31 21:05:52,071 - WARNING - 未找到候选文件: frame119_from_06229_WebUAV3M_train_pagoda_16_3_BEST.mp4
2025-07-31 21:05:52,098 - WARNING - 未找到候选文件: frame119_from_07250_WebUAV3M_train_riding_an_electric_bicycle_1_0_BEST.mp4
2025-07-31 21:05:52,126 - WARNING - 未找到候选文件: frame11_from_03729_WebUAV3M_train_calling_8_1_BEST.mp4
2025-07-31 21:05:52,154 - WARNING - 未找到候选文件: frame11_from_04507_WebUAV3M_train_elephant_3_0_BEST.mp4
2025-07-31 21:05:52,182 - WARNING - 未找到候选文件: frame11_from_04571_WebUAV3M_train_ferry_1_1_BEST.mp4
2025-07-31 21:05:52,209 - WARNING - 未找到候选文件: frame120_from_01564_NAT2021_train_0000leaf1_0_BEST.mp4
2025-07-31 21:05:52,237 - WARNING - 未找到候选文件: frame120_from_03755_WebUAV3M_train_carrying_12_2_BEST.mp4
2025-07-31 21:05:52,267 - INFO - 匹配成功: frame120_from_04625_WebUAV3M_train_ferry_7_2_BEST.mp4 -> frame120_from_04625_WebUAV3M_train_ferry_7_2_3_seed1.mp4
2025-07-31 21:05:52,295 - WARNING - 未找到候选文件: frame120_from_07242_WebUAV3M_train_remote_control_boat_1_4_BEST.mp4
2025-07-31 21:05:52,323 - WARNING - 未找到候选文件: frame121_from_00028_urbanvideo_train_BEST.mp4
2025-07-31 21:05:52,350 - WARNING - 未找到候选文件: frame121_from_04407_WebUAV3M_train_dolphin_4_0_BEST.mp4
2025-07-31 21:05:52,381 - INFO - 匹配成功: frame122_from_00191_urbanvideo_train_BEST.mp4 -> frame122_from_00191_urbanvideo_train_1_seed42.mp4
2025-07-31 21:05:52,409 - WARNING - 未找到候选文件: frame122_from_02219_NAT2021_train_0404person3_1_0_BEST.mp4
2025-07-31 21:05:52,444 - INFO - 匹配成功: frame122_from_06167_WebUAV3M_train_oil_tanker_1_1_BEST.mp4 -> frame122_from_06167_WebUAV3M_train_oil_tanker_1_1_3_results_seed1.mp4
2025-07-31 21:05:52,472 - WARNING - 未找到候选文件: frame123_from_03649_WebUAV3M_train_cable_car_8_1_BEST.mp4
2025-07-31 21:05:52,513 - INFO - 匹配成功: frame123_from_03768_WebUAV3M_train_carrying_14_5_BEST.mp4 -> frame123_from_03768_WebUAV3M_train_carrying_14_5_7_results_seed42.mp4
2025-07-31 21:05:52,541 - WARNING - 未找到候选文件: frame123_from_03867_WebUAV3M_train_carrying_37_0_BEST.mp4
2025-07-31 21:05:52,578 - INFO - 匹配成功: frame123_from_03892_WebUAV3M_train_castle_2_1_BEST.mp4 -> frame123_from_03892_WebUAV3M_train_castle_2_1_5_results_seed42.mp4
2025-07-31 21:05:52,606 - WARNING - 未找到候选文件: frame123_from_04627_WebUAV3M_train_ferry_8_1_BEST.mp4
2025-07-31 21:05:52,634 - WARNING - 未找到候选文件: frame123_from_07113_WebUAV3M_train_rapid_transit_3_4_BEST.mp4
2025-07-31 21:05:52,662 - WARNING - 未找到候选文件: frame124_from_00441_urbanvideo_train_BEST.mp4
2025-07-31 21:05:52,690 - WARNING - 未找到候选文件: frame124_from_01998_NAT2021_train_0175car1_2_0_BEST.mp4
2025-07-31 21:05:52,717 - WARNING - 未找到候选文件: frame124_from_04449_WebUAV3M_train_dropside_truck_26_0_BEST.mp4
2025-07-31 21:05:52,746 - INFO - 匹配成功: frame124_from_07156_WebUAV3M_train_reading_19_1_BEST.mp4 -> frame124_from_07156_WebUAV3M_train_reading_19_1_1_seed1.mp4
2025-07-31 21:05:52,774 - WARNING - 未找到候选文件: frame125_from_00403_urbanvideo_train_BEST.mp4
2025-07-31 21:05:52,813 - INFO - 匹配成功: frame125_from_00585_urbanvideo_train_BEST.mp4 -> frame125_from_00585_urbanvideo_train_7_seed1.mp4
2025-07-31 21:05:52,841 - WARNING - 未找到候选文件: frame125_from_03668_WebUAV3M_train_cab_19_0_BEST.mp4
2025-07-31 21:05:52,870 - WARNING - 未找到候选文件: frame125_from_03825_WebUAV3M_train_carrying_28_5_BEST.mp4
2025-07-31 21:05:52,898 - WARNING - 未找到候选文件: frame125_from_04216_WebUAV3M_train_container_truck_21_0_BEST.mp4
2025-07-31 21:05:52,925 - WARNING - 未找到候选文件: frame125_from_04677_WebUAV3M_train_fox_1_0_BEST.mp4
2025-07-31 21:05:52,962 - INFO - 匹配成功: frame125_from_05008_WebUAV3M_train_harvester_46_0_BEST.mp4 -> frame125_from_05008_WebUAV3M_train_harvester_46_0_3_seed1.mp4
2025-07-31 21:05:52,991 - WARNING - 未找到候选文件: frame126_from_00239_urbanvideo_train_BEST.mp4
2025-07-31 21:05:53,019 - WARNING - 未找到候选文件: frame126_from_00273_urbanvideo_train_BEST.mp4
2025-07-31 21:05:53,047 - WARNING - 未找到候选文件: frame126_from_00285_urbanvideo_train_BEST.mp4
2025-07-31 21:05:53,075 - WARNING - 未找到候选文件: frame126_from_02210_NAT2021_train_0404person2_10_0_BEST.mp4
2025-07-31 21:05:53,103 - WARNING - 未找到候选文件: frame126_from_02261_NAT2021_train_0408car2_0_BEST.mp4
2025-07-31 21:05:53,137 - INFO - 匹配成功: frame126_from_06129_WebUAV3M_train_office_building_71_2_BEST.mp4 -> frame126_from_06129_WebUAV3M_train_office_building_71_2_5_seed1.mp4
2025-07-31 21:05:53,171 - INFO - 匹配成功: frame127_from_02673_NAT2021_train_0427runner1_15_0_BEST.mp4 -> frame127_from_02673_NAT2021_train_0427runner1_15_0_6_seed1.mp4
2025-07-31 21:05:53,199 - WARNING - 未找到候选文件: frame127_from_04253_WebUAV3M_train_cow_9_0_BEST.mp4
2025-07-31 21:05:53,230 - INFO - 匹配成功: frame127_from_04417_WebUAV3M_train_double-decker_bus_1_0_BEST.mp4 -> frame127_from_04417_WebUAV3M_train_double-decker_bus_1_0_1_seed42.mp4
2025-07-31 21:05:53,258 - WARNING - 未找到候选文件: frame128_from_04421_WebUAV3M_train_double-decker_bus_4_0_BEST.mp4
2025-07-31 21:05:53,286 - WARNING - 未找到候选文件: frame128_from_04630_WebUAV3M_train_filming_1_0_BEST.mp4
2025-07-31 21:05:53,314 - WARNING - 未找到候选文件: frame12_from_00334_urbanvideo_train_BEST.mp4
2025-07-31 21:05:53,342 - WARNING - 未找到候选文件: frame12_from_00637_urbanvideo_train_BEST.mp4
2025-07-31 21:05:53,373 - INFO - 匹配成功: frame12_from_06283_WebUAV3M_train_pagoda_26_7_BEST.mp4 -> frame12_from_06283_WebUAV3M_train_pagoda_26_7_3_results_seed42.mp4
2025-07-31 21:05:53,401 - WARNING - 未找到候选文件: frame12_from_06297_WebUAV3M_train_pagoda_28_6_BEST.mp4
2025-07-31 21:05:53,437 - INFO - 匹配成功: frame13_from_01490_NAT2021_train_0000car23_15_0_BEST.mp4 -> frame13_from_01490_NAT2021_train_0000car23_15_0_8_seed42.mp4
2025-07-31 21:05:53,481 - INFO - 匹配成功: frame13_from_01716_NAT2021_train_0131ship1_10_0_BEST.mp4 -> frame13_from_01716_NAT2021_train_0131ship1_10_0_7_results_seed42.mp4
2025-07-31 21:05:53,510 - WARNING - 未找到候选文件: frame13_from_02111_NAT2021_train_0264person4_3_0_BEST.mp4
2025-07-31 21:05:53,538 - WARNING - 未找到候选文件: frame13_from_04438_WebUAV3M_train_dropside_truck_20_1_BEST.mp4
2025-07-31 21:05:53,566 - WARNING - 未找到候选文件: frame14_from_01650_NAT2021_train_0000sign4_0_BEST.mp4
2025-07-31 21:05:53,594 - WARNING - 未找到候选文件: frame14_from_02924_WebUAV3M_train_beacon_21_1_BEST.mp4
2025-07-31 21:05:53,646 - INFO - 匹配成功: frame14_from_04328_WebUAV3M_train_digging_bucket_14_2_BEST.mp4 -> frame14_from_04328_WebUAV3M_train_digging_bucket_14_2_8_seed42.mp4
2025-07-31 21:05:53,677 - INFO - 匹配成功: frame15_from_07213_WebUAV3M_train_reading_8_0_BEST.mp4 -> frame15_from_07213_WebUAV3M_train_reading_8_0_3_seed1.mp4
2025-07-31 21:05:53,705 - WARNING - 未找到候选文件: frame15_from_07266_WebUAV3M_train_riding_an_electric_bicycle_24_1_BEST.mp4
2025-07-31 21:05:53,736 - INFO - 匹配成功: frame16_from_02015_NAT2021_train_0186building1_1_0_BEST.mp4 -> frame16_from_02015_NAT2021_train_0186building1_1_0_3_seed42.mp4
2025-07-31 21:05:53,764 - WARNING - 未找到候选文件: frame16_from_03974_WebUAV3M_train_chimney_17_2_BEST.mp4
2025-07-31 21:05:53,792 - WARNING - 未找到候选文件: frame16_from_04466_WebUAV3M_train_dropside_truck_34_0_BEST.mp4
2025-07-31 21:05:53,821 - INFO - 匹配成功: frame17_from_02404_NAT2021_train_0420car1_5_0_BEST.mp4 -> frame17_from_02404_NAT2021_train_0420car1_5_0_3_seed42.mp4
2025-07-31 21:05:53,851 - INFO - 匹配成功: frame17_from_02699_NAT2021_train_0427runner6_3_0_BEST.mp4 -> frame17_from_02699_NAT2021_train_0427runner6_3_0_4_seed1.mp4
2025-07-31 21:05:53,879 - WARNING - 未找到候选文件: frame17_from_02799_WebUAV3M_train_apartment_12_0_BEST.mp4
2025-07-31 21:05:53,917 - INFO - 匹配成功: frame17_from_03394_WebUAV3M_train_box_truck_13_1_BEST.mp4 -> frame17_from_03394_WebUAV3M_train_box_truck_13_1_5_seed1.mp4
2025-07-31 21:05:53,944 - WARNING - 未找到候选文件: frame17_from_04601_WebUAV3M_train_ferry_22_1_BEST.mp4
2025-07-31 21:05:53,978 - INFO - 匹配成功: frame17_from_05166_WebUAV3M_train_hatchback_90_1_BEST.mp4 -> frame17_from_05166_WebUAV3M_train_hatchback_90_1_2_seed1.mp4
2025-07-31 21:05:54,006 - WARNING - 未找到候选文件: frame18_from_00693_urbanvideo_train_BEST.mp4
2025-07-31 21:05:54,038 - INFO - 匹配成功: frame18_from_02242_NAT2021_train_0408bus1_1_0_BEST.mp4 -> frame18_from_02242_NAT2021_train_0408bus1_1_0_3_results_seed1.mp4
2025-07-31 21:05:54,066 - WARNING - 未找到候选文件: frame18_from_02847_WebUAV3M_train_bamboo_raft_5_0_BEST.mp4
2025-07-31 21:05:54,133 - INFO - 匹配成功: frame18_from_03455_WebUAV3M_train_box_truck_48_0_BEST.mp4 -> frame18_from_03455_WebUAV3M_train_box_truck_48_0_8_results_seed42.mp4
2025-07-31 21:05:54,161 - WARNING - 未找到候选文件: frame18_from_05092_WebUAV3M_train_hatchback_29_1_BEST.mp4
2025-07-31 21:05:54,189 - WARNING - 未找到候选文件: frame19_from_04233_WebUAV3M_train_cow_10_0_BEST.mp4
2025-07-31 21:05:54,220 - INFO - 匹配成功: frame19_from_06200_WebUAV3M_train_ox_9_0_BEST.mp4 -> frame19_from_06200_WebUAV3M_train_ox_9_0_1_results_seed42.mp4
2025-07-31 21:05:54,247 - WARNING - 未找到候选文件: frame1_from_00367_urbanvideo_train_BEST.mp4
2025-07-31 21:05:54,275 - WARNING - 未找到候选文件: frame1_from_02804_WebUAV3M_train_apartment_15_0_BEST.mp4
2025-07-31 21:05:54,305 - INFO - 匹配成功: frame1_from_02849_WebUAV3M_train_bamboo_raft_6_0_BEST.mp4 -> frame1_from_02849_WebUAV3M_train_bamboo_raft_6_0_1_seed42.mp4
2025-07-31 21:05:54,333 - WARNING - 未找到候选文件: frame1_from_03669_WebUAV3M_train_cab_2_0_BEST.mp4
2025-07-31 21:05:54,377 - INFO - 匹配成功: frame1_from_03955_WebUAV3M_train_chimney_1_8_BEST.mp4 -> frame1_from_03955_WebUAV3M_train_chimney_1_8_8_seed1.mp4
2025-07-31 21:05:54,405 - WARNING - 未找到候选文件: frame21_from_00003_urbanvideo_train_BEST.mp4
2025-07-31 21:05:54,433 - WARNING - 未找到候选文件: frame21_from_03652_WebUAV3M_train_cable_car_8_4_BEST.mp4
2025-07-31 21:05:54,464 - INFO - 匹配成功: frame22_from_01759_NAT2021_train_0133car2_6_0_BEST.mp4 -> frame22_from_01759_NAT2021_train_0133car2_6_0_2_results_seed42.mp4
2025-07-31 21:05:54,492 - WARNING - 未找到候选文件: frame22_from_02015_NAT2021_train_0186building1_1_0_BEST.mp4
2025-07-31 21:05:54,526 - INFO - 匹配成功: frame22_from_02846_WebUAV3M_train_bamboo_raft_4_1_BEST.mp4 -> frame22_from_02846_WebUAV3M_train_bamboo_raft_4_1_3_results_seed42.mp4
2025-07-31 21:05:54,556 - INFO - 匹配成功: frame22_from_05087_WebUAV3M_train_hatchback_23_1_BEST.mp4 -> frame22_from_05087_WebUAV3M_train_hatchback_23_1_1_seed42.mp4
2025-07-31 21:05:54,584 - WARNING - 未找到候选文件: frame22_from_06230_WebUAV3M_train_pagoda_16_4_BEST.mp4
2025-07-31 21:05:54,612 - WARNING - 未找到候选文件: frame23_from_00469_urbanvideo_train_BEST.mp4
2025-07-31 21:05:54,640 - WARNING - 未找到候选文件: frame23_from_01876_NAT2021_train_0138person4_15_0_BEST.mp4
2025-07-31 21:05:54,667 - WARNING - 未找到候选文件: frame23_from_02703_NAT2021_train_0427truck1_11_0_BEST.mp4
2025-07-31 21:05:54,697 - INFO - 匹配成功: frame23_from_02939_WebUAV3M_train_beacon_26_4_BEST.mp4 -> frame23_from_02939_WebUAV3M_train_beacon_26_4_1_seed1.mp4
2025-07-31 21:05:54,725 - WARNING - 未找到候选文件: frame23_from_03664_WebUAV3M_train_cab_16_2_BEST.mp4
2025-07-31 21:05:54,753 - WARNING - 未找到候选文件: frame25_from_00459_urbanvideo_train_BEST.mp4
2025-07-31 21:05:54,781 - WARNING - 未找到候选文件: frame25_from_00721_urbanvideo_train_BEST.mp4
2025-07-31 21:05:54,817 - INFO - 匹配成功: frame25_from_01772_NAT2021_train_0136car1_11_0_BEST.mp4 -> frame25_from_01772_NAT2021_train_0136car1_11_0_5_seed42.mp4
2025-07-31 21:05:54,861 - INFO - 匹配成功: frame25_from_07330_WebUAV3M_train_riding_an_electric_bicycle_9_1_BEST.mp4 -> frame25_from_07330_WebUAV3M_train_riding_an_electric_bicycle_9_1_3_results_seed42.mp4
2025-07-31 21:05:54,917 - INFO - 匹配成功: frame26_from_00222_urbanvideo_train_BEST.mp4 -> frame26_from_00222_urbanvideo_train_7_results_seed1.mp4
2025-07-31 21:05:54,951 - INFO - 匹配成功: frame26_from_00736_urbanvideo_train_BEST.mp4 -> frame26_from_00736_urbanvideo_train_2_results_seed1.mp4
2025-07-31 21:05:54,988 - INFO - 匹配成功: frame26_from_02326_NAT2021_train_0409bridge1_1_0_BEST.mp4 -> frame26_from_02326_NAT2021_train_0409bridge1_1_0_6_results_seed42.mp4
2025-07-31 21:05:55,021 - INFO - 匹配成功: frame26_from_07245_WebUAV3M_train_rice_transplanter_1_0_BEST.mp4 -> frame26_from_07245_WebUAV3M_train_rice_transplanter_1_0_2_seed42.mp4
2025-07-31 21:05:55,049 - WARNING - 未找到候选文件: frame27_from_02445_NAT2021_train_0421car11_2_0_BEST.mp4
2025-07-31 21:05:55,088 - INFO - 匹配成功: frame28_from_01930_NAT2021_train_0139car17_4_0_BEST.mp4 -> frame28_from_01930_NAT2021_train_0139car17_4_0_5_results_seed1.mp4
2025-07-31 21:05:55,119 - INFO - 匹配成功: frame29_from_01944_NAT2021_train_0173building1_2_0_BEST.mp4 -> frame29_from_01944_NAT2021_train_0173building1_2_0_3_seed1.mp4
2025-07-31 21:05:55,155 - INFO - 匹配成功: frame29_from_04406_WebUAV3M_train_dolphin_3_2_BEST.mp4 -> frame29_from_04406_WebUAV3M_train_dolphin_3_2_3_results_seed42.mp4
2025-07-31 21:05:55,189 - INFO - 匹配成功: frame2_from_02201_NAT2021_train_0404person1_2_0_BEST.mp4 -> frame2_from_02201_NAT2021_train_0404person1_2_0_7_results_seed1.mp4
2025-07-31 21:05:55,218 - INFO - 匹配成功: frame2_from_02402_NAT2021_train_0420car1_3_0_BEST.mp4 -> frame2_from_02402_NAT2021_train_0420car1_3_0_3_seed42.mp4
2025-07-31 21:05:55,247 - INFO - 匹配成功: frame2_from_02514_NAT2021_train_0421truck2_12_0_BEST.mp4 -> frame2_from_02514_NAT2021_train_0421truck2_12_0_2_seed42.mp4
2025-07-31 21:05:55,281 - INFO - 匹配成功: frame2_from_03760_WebUAV3M_train_carrying_13_3_BEST.mp4 -> frame2_from_03760_WebUAV3M_train_carrying_13_3_4_seed42.mp4
2025-07-31 21:05:55,309 - WARNING - 未找到候选文件: frame2_from_07247_WebUAV3M_train_rice_transplanter_1_2_BEST.mp4
2025-07-31 21:05:55,337 - WARNING - 未找到候选文件: frame30_from_02664_NAT2021_train_0427motor1_4_0_BEST.mp4
2025-07-31 21:05:55,365 - WARNING - 未找到候选文件: frame30_from_03650_WebUAV3M_train_cable_car_8_2_BEST.mp4
2025-07-31 21:05:55,393 - WARNING - 未找到候选文件: frame30_from_04009_WebUAV3M_train_chimney_5_7_BEST.mp4
2025-07-31 21:05:55,420 - WARNING - 未找到候选文件: frame31_from_02448_NAT2021_train_0421car11_5_0_BEST.mp4
2025-07-31 21:05:55,448 - WARNING - 未找到候选文件: frame31_from_05178_WebUAV3M_train_hatchback_95_0_BEST.mp4
2025-07-31 21:05:55,476 - WARNING - 未找到候选文件: frame31_from_06034_WebUAV3M_train_office_building_35_1_BEST.mp4
2025-07-31 21:05:55,504 - WARNING - 未找到候选文件: frame31_from_06249_WebUAV3M_train_pagoda_18_8_BEST.mp4
2025-07-31 21:05:55,533 - WARNING - 未找到候选文件: frame32_from_00701_urbanvideo_train_BEST.mp4
2025-07-31 21:05:55,561 - WARNING - 未找到候选文件: frame32_from_00755_urbanvideo_train_BEST.mp4
2025-07-31 21:05:55,598 - INFO - 匹配成功: frame32_from_02957_WebUAV3M_train_beacon_34_0_BEST.mp4 -> frame32_from_02957_WebUAV3M_train_beacon_34_0_6_results_seed1.mp4
2025-07-31 21:05:55,630 - INFO - 匹配成功: frame32_from_04234_WebUAV3M_train_cow_11_0_BEST.mp4 -> frame32_from_04234_WebUAV3M_train_cow_11_0_4_seed1.mp4
2025-07-31 21:05:55,677 - INFO - 匹配成功: frame32_from_04518_WebUAV3M_train_excavator_1_1_BEST.mp4 -> frame32_from_04518_WebUAV3M_train_excavator_1_1_7_results_seed1.mp4
2025-07-31 21:05:55,711 - INFO - 匹配成功: frame32_from_05923_WebUAV3M_train_motorboat_58_1_BEST.mp4 -> frame32_from_05923_WebUAV3M_train_motorboat_58_1_2_results_seed1.mp4
2025-07-31 21:05:55,741 - INFO - 匹配成功: frame32_from_06041_WebUAV3M_train_office_building_38_0_BEST.mp4 -> frame32_from_06041_WebUAV3M_train_office_building_38_0_2_seed1.mp4
2025-07-31 21:05:55,786 - INFO - 匹配成功: frame33_from_00141_urbanvideo_train_BEST.mp4 -> frame33_from_00141_urbanvideo_train_8_seed1.mp4
2025-07-31 21:05:55,832 - INFO - 匹配成功: frame33_from_01825_NAT2021_train_0138car9_6_0_BEST.mp4 -> frame33_from_01825_NAT2021_train_0138car9_6_0_5_results_seed1.mp4
2025-07-31 21:05:55,872 - INFO - 匹配成功: frame33_from_03988_WebUAV3M_train_chimney_19_3_BEST.mp4 -> frame33_from_03988_WebUAV3M_train_chimney_19_3_8_seed42.mp4
2025-07-31 21:05:55,907 - INFO - 匹配成功: frame34_from_03548_WebUAV3M_train_bulk_carrier_22_3_BEST.mp4 -> frame34_from_03548_WebUAV3M_train_bulk_carrier_22_3_8_seed1.mp4
2025-07-31 21:05:55,952 - INFO - 匹配成功: frame34_from_04517_WebUAV3M_train_excavator_1_0_BEST.mp4 -> frame34_from_04517_WebUAV3M_train_excavator_1_0_8_results_seed1.mp4
2025-07-31 21:05:55,984 - INFO - 匹配成功: frame34_from_07316_WebUAV3M_train_riding_an_electric_bicycle_6_0_BEST.mp4 -> frame34_from_07316_WebUAV3M_train_riding_an_electric_bicycle_6_0_2_seed1.mp4
2025-07-31 21:05:56,018 - INFO - 匹配成功: frame35_from_02268_NAT2021_train_0408car3_6_0_BEST.mp4 -> frame35_from_02268_NAT2021_train_0408car3_6_0_8_seed42.mp4
2025-07-31 21:05:56,051 - INFO - 匹配成功: frame35_from_05176_WebUAV3M_train_hatchback_94_1_BEST.mp4 -> frame35_from_05176_WebUAV3M_train_hatchback_94_1_2_results_seed1.mp4
2025-07-31 21:05:56,087 - INFO - 匹配成功: frame36_from_04939_WebUAV3M_train_harvester_25_0_BEST.mp4 -> frame36_from_04939_WebUAV3M_train_harvester_25_0_5_seed42.mp4
2025-07-31 21:05:56,118 - INFO - 匹配成功: frame37_from_00027_urbanvideo_train_BEST.mp4 -> frame37_from_00027_urbanvideo_train_1_seed1.mp4
2025-07-31 21:05:56,147 - INFO - 匹配成功: frame37_from_06251_WebUAV3M_train_pagoda_18_10_BEST.mp4 -> frame37_from_06251_WebUAV3M_train_pagoda_18_10_1_seed1.mp4
2025-07-31 21:05:56,182 - INFO - 匹配成功: frame38_from_04606_WebUAV3M_train_ferry_25_0_BEST.mp4 -> frame38_from_04606_WebUAV3M_train_ferry_25_0_5_results_seed42.mp4
2025-07-31 21:05:56,225 - INFO - 匹配成功: frame39_from_00700_urbanvideo_train_BEST.mp4 -> frame39_from_00700_urbanvideo_train_5_seed42.mp4
2025-07-31 21:05:56,259 - INFO - 匹配成功: frame3_from_00439_urbanvideo_train_BEST.mp4 -> frame3_from_00439_urbanvideo_train_4_results_seed42.mp4
2025-07-31 21:05:56,291 - INFO - 匹配成功: frame40_from_00169_urbanvideo_train_BEST.mp4 -> frame40_from_00169_urbanvideo_train_3_seed42.mp4
2025-07-31 21:05:56,334 - INFO - 匹配成功: frame40_from_00228_urbanvideo_train_BEST.mp4 -> frame40_from_00228_urbanvideo_train_4_results_seed1.mp4
2025-07-31 21:05:56,367 - INFO - 匹配成功: frame40_from_02968_WebUAV3M_train_beacon_9_0_BEST.mp4 -> frame40_from_02968_WebUAV3M_train_beacon_9_0_5_results_seed42.mp4
2025-07-31 21:05:56,407 - INFO - 匹配成功: frame40_from_03966_WebUAV3M_train_chimney_13_7_BEST.mp4 -> frame40_from_03966_WebUAV3M_train_chimney_13_7_5_results_seed42.mp4
2025-07-31 21:05:56,454 - INFO - 匹配成功: frame41_from_00477_urbanvideo_train_BEST.mp4 -> frame41_from_00477_urbanvideo_train_7_results_seed1.mp4
2025-07-31 21:05:56,502 - INFO - 匹配成功: frame43_from_06087_WebUAV3M_train_office_building_55_1_BEST.mp4 -> frame43_from_06087_WebUAV3M_train_office_building_55_1_7_results_seed42.mp4
2025-07-31 21:05:56,531 - INFO - 匹配成功: frame44_from_01602_NAT2021_train_0000person6_21_0_BEST.mp4 -> frame44_from_01602_NAT2021_train_0000person6_21_0_2_seed1.mp4
2025-07-31 21:05:56,560 - INFO - 匹配成功: frame45_from_01661_NAT2021_train_0130car1_13_0_BEST.mp4 -> frame45_from_01661_NAT2021_train_0130car1_13_0_1_seed42.mp4
2025-07-31 21:05:56,588 - WARNING - 未找到候选文件: frame46_from_06102_WebUAV3M_train_office_building_6_2_BEST.mp4
2025-07-31 21:05:56,615 - WARNING - 未找到候选文件: frame47_from_00419_urbanvideo_train_BEST.mp4
2025-07-31 21:05:56,643 - WARNING - 未找到候选文件: frame46_from_06121_WebUAV3M_train_office_building_69_0_BEST.mp4
2025-07-31 21:05:56,671 - WARNING - 未找到候选文件: frame47_from_03999_WebUAV3M_train_chimney_2_5_BEST.mp4
2025-07-31 21:05:56,699 - WARNING - 未找到候选文件: frame47_from_04538_WebUAV3M_train_ferris_wheel_2_0_BEST.mp4
2025-07-31 21:05:56,751 - INFO - 匹配成功: frame47_from_07342_WebUAV3M_train_riding_a_horse_4_6_BEST.mp4 -> frame47_from_07342_WebUAV3M_train_riding_a_horse_4_6_6_results_seed42.mp4
2025-07-31 21:05:56,780 - INFO - 匹配成功: frame48_from_01411_NAT2021_train_0000car13_2_0_BEST.mp4 -> frame48_from_01411_NAT2021_train_0000car13_2_0_1_seed42.mp4
2025-07-31 21:05:56,808 - WARNING - 未找到候选文件: frame48_from_02617_NAT2021_train_0426person3_8_0_BEST.mp4
2025-07-31 21:05:56,858 - INFO - 匹配成功: frame48_from_03650_WebUAV3M_train_cable_car_8_2_BEST.mp4 -> frame48_from_03650_WebUAV3M_train_cable_car_8_2_8_results_seed42.mp4
2025-07-31 21:05:56,895 - INFO - 匹配成功: frame48_from_04566_WebUAV3M_train_ferris_wheel_pod_2_0_BEST.mp4 -> frame48_from_04566_WebUAV3M_train_ferris_wheel_pod_2_0_4_results_seed42.mp4
2025-07-31 21:05:56,923 - WARNING - 未找到候选文件: frame48_from_05002_WebUAV3M_train_harvester_45_0_BEST.mp4
2025-07-31 21:05:56,951 - WARNING - 未找到候选文件: frame49_from_00556_urbanvideo_train_BEST.mp4
2025-07-31 21:05:56,979 - WARNING - 未找到候选文件: frame49_from_02101_NAT2021_train_0264person3_2_0_BEST.mp4
2025-07-31 21:05:57,008 - INFO - 匹配成功: frame49_from_02671_NAT2021_train_0427runner1_13_0_BEST.mp4 -> frame49_from_02671_NAT2021_train_0427runner1_13_0_1_results_seed1.mp4
2025-07-31 21:05:57,038 - INFO - 匹配成功: frame49_from_03915_WebUAV3M_train_catboat_1_4_BEST.mp4 -> frame49_from_03915_WebUAV3M_train_catboat_1_4_2_results_seed42.mp4
2025-07-31 21:05:57,066 - WARNING - 未找到候选文件: frame4_from_00122_urbanvideo_train_BEST.mp4
2025-07-31 21:05:57,094 - WARNING - 未找到候选文件: frame4_from_00775_urbanvideo_train_BEST.mp4
2025-07-31 21:05:57,143 - INFO - 匹配成功: frame4_from_02115_NAT2021_train_0264person5_3_0_BEST.mp4 -> frame4_from_02115_NAT2021_train_0264person5_3_0_7_results_seed42.mp4
2025-07-31 21:05:57,171 - WARNING - 未找到候选文件: frame4_from_03495_WebUAV3M_train_brown_bear_3_2_BEST.mp4
2025-07-31 21:05:57,216 - INFO - 匹配成功: frame4_from_04386_WebUAV3M_train_dog_3_7_BEST.mp4 -> frame4_from_04386_WebUAV3M_train_dog_3_7_3_results_seed1.mp4
2025-07-31 21:05:57,244 - WARNING - 未找到候选文件: frame4_from_06106_WebUAV3M_train_office_building_62_0_BEST.mp4
2025-07-31 21:05:57,280 - INFO - 匹配成功: frame4_from_06149_WebUAV3M_train_office_building_8_1_BEST.mp4 -> frame4_from_06149_WebUAV3M_train_office_building_8_1_5_results_seed1.mp4
2025-07-31 21:05:57,319 - INFO - 匹配成功: frame4_from_07267_WebUAV3M_train_riding_an_electric_bicycle_25_0_BEST.mp4 -> frame4_from_07267_WebUAV3M_train_riding_an_electric_bicycle_25_0_4_seed1.mp4
2025-07-31 21:05:57,347 - WARNING - 未找到候选文件: frame50_from_00323_urbanvideo_train_BEST.mp4
2025-07-31 21:05:57,375 - WARNING - 未找到候选文件: frame50_from_01632_NAT2021_train_0000rider4_9_0_BEST.mp4
2025-07-31 21:05:57,402 - WARNING - 未找到候选文件: frame50_from_02246_NAT2021_train_0408bus2_3_0_BEST.mp4
2025-07-31 21:05:57,430 - WARNING - 未找到候选文件: frame50_from_03823_WebUAV3M_train_carrying_28_3_BEST.mp4
2025-07-31 21:05:57,474 - INFO - 匹配成功: frame51_from_00724_urbanvideo_train_BEST.mp4 -> frame51_from_00724_urbanvideo_train_4_results_seed1.mp4
2025-07-31 21:05:57,504 - INFO - 匹配成功: frame51_from_02086_NAT2021_train_0264person1_2_0_BEST.mp4 -> frame51_from_02086_NAT2021_train_0264person1_2_0_4_seed42.mp4
2025-07-31 21:05:57,531 - WARNING - 未找到候选文件: frame51_from_02834_WebUAV3M_train_balloon_3_0_BEST.mp4
2025-07-31 21:05:57,565 - INFO - 匹配成功: frame51_from_03882_WebUAV3M_train_carrying_8_1_BEST.mp4 -> frame51_from_03882_WebUAV3M_train_carrying_8_1_7_seed1.mp4
2025-07-31 21:05:57,599 - INFO - 匹配成功: frame51_from_04935_WebUAV3M_train_harvester_21_0_BEST.mp4 -> frame51_from_04935_WebUAV3M_train_harvester_21_0_6_seed1.mp4
2025-07-31 21:05:57,628 - WARNING - 未找到候选文件: frame51_from_06050_WebUAV3M_train_office_building_40_1_BEST.mp4
2025-07-31 21:05:57,655 - WARNING - 未找到候选文件: frame51_from_06095_WebUAV3M_train_office_building_58_0_BEST.mp4
2025-07-31 21:05:57,683 - WARNING - 未找到候选文件: frame52_from_03480_WebUAV3M_train_box_truck_64_1_BEST.mp4
2025-07-31 21:05:57,726 - INFO - 匹配成功: frame52_from_04473_WebUAV3M_train_dropside_truck_6_0_BEST.mp4 -> frame52_from_04473_WebUAV3M_train_dropside_truck_6_0_5_results_seed1.mp4
2025-07-31 21:05:57,755 - INFO - 匹配成功: frame52_from_04561_WebUAV3M_train_ferris_wheel_pod_1_0_BEST.mp4 -> frame52_from_04561_WebUAV3M_train_ferris_wheel_pod_1_0_1_seed1.mp4
2025-07-31 21:05:57,782 - WARNING - 未找到候选文件: frame52_from_05016_WebUAV3M_train_harvester_52_0_BEST.mp4
2025-07-31 21:05:57,829 - INFO - 匹配成功: frame53_from_00063_urbanvideo_train_BEST.mp4 -> frame53_from_00063_urbanvideo_train_6_results_seed42.mp4
2025-07-31 21:05:57,857 - WARNING - 未找到候选文件: frame53_from_00117_urbanvideo_train_BEST.mp4
2025-07-31 21:05:57,885 - WARNING - 未找到候选文件: frame53_from_00402_urbanvideo_train_BEST.mp4
2025-07-31 21:05:57,913 - WARNING - 未找到候选文件: frame53_from_00426_urbanvideo_train_BEST.mp4
2025-07-31 21:05:57,940 - WARNING - 未找到候选文件: frame53_from_00510_urbanvideo_train_BEST.mp4
2025-07-31 21:05:57,968 - WARNING - 未找到候选文件: frame53_from_02969_WebUAV3M_train_bell_tower_1_0_BEST.mp4
2025-07-31 21:05:57,996 - WARNING - 未找到候选文件: frame53_from_04647_WebUAV3M_train_flyboarding_10_2_BEST.mp4
2025-07-31 21:05:58,024 - WARNING - 未找到候选文件: frame53_from_04895_WebUAV3M_train_goose_1_2_BEST.mp4
2025-07-31 21:05:58,057 - INFO - 匹配成功: frame53_from_07222_WebUAV3M_train_red_star_1_4_BEST.mp4 -> frame53_from_07222_WebUAV3M_train_red_star_1_4_4_seed1.mp4
2025-07-31 21:05:58,085 - WARNING - 未找到候选文件: frame54_from_02596_NAT2021_train_0425person1_5_0_BEST.mp4
2025-07-31 21:05:58,112 - WARNING - 未找到候选文件: frame54_from_04718_WebUAV3M_train_gaily-painted_pleasure-boat_4_10_BEST.mp4
2025-07-31 21:05:58,151 - INFO - 匹配成功: frame55_from_02941_WebUAV3M_train_beacon_26_6_BEST.mp4 -> frame55_from_02941_WebUAV3M_train_beacon_26_6_6_seed42.mp4
2025-07-31 21:05:58,179 - WARNING - 未找到候选文件: frame59_from_04745_WebUAV3M_train_gaily-painted_pleasure-boat_9_2_BEST.mp4
2025-07-31 21:05:58,206 - WARNING - 未找到候选文件: frame60_from_01661_NAT2021_train_0130car1_13_0_BEST.mp4
2025-07-31 21:05:58,234 - WARNING - 未找到候选文件: frame60_from_05929_WebUAV3M_train_motorboat_7_0_BEST.mp4
2025-07-31 21:05:58,262 - WARNING - 未找到候选文件: frame60_from_05949_WebUAV3M_train_ocean_liner_2_0_BEST.mp4
2025-07-31 21:05:58,289 - WARNING - 未找到候选文件: frame61_from_00754_urbanvideo_train_BEST.mp4
2025-07-31 21:05:58,319 - INFO - 匹配成功: frame61_from_01715_NAT2021_train_0131ship1_1_0_BEST.mp4 -> frame61_from_01715_NAT2021_train_0131ship1_1_0_2_seed1.mp4
2025-07-31 21:05:58,347 - WARNING - 未找到候选文件: frame61_from_03435_WebUAV3M_train_box_truck_4_0_BEST.mp4
2025-07-31 21:05:58,375 - WARNING - 未找到候选文件: frame61_from_04424_WebUAV3M_train_driving_1_1_BEST.mp4
2025-07-31 21:05:58,402 - WARNING - 未找到候选文件: frame61_from_04637_WebUAV3M_train_fishing_boat_2_0_BEST.mp4
2025-07-31 21:05:58,430 - WARNING - 未找到候选文件: frame61_from_06042_WebUAV3M_train_office_building_38_1_BEST.mp4
2025-07-31 21:05:58,457 - WARNING - 未找到候选文件: frame61_from_07024_WebUAV3M_train_pushing_a_cart_7_0_BEST.mp4
2025-07-31 21:05:58,485 - WARNING - 未找到候选文件: frame61_from_07274_WebUAV3M_train_riding_an_electric_bicycle_3_0_BEST.mp4
2025-07-31 21:05:58,513 - WARNING - 未找到候选文件: frame62_from_00389_urbanvideo_train_BEST.mp4
2025-07-31 21:05:58,541 - WARNING - 未找到候选文件: frame62_from_01708_NAT2021_train_0130truck2_7_0_BEST.mp4
2025-07-31 21:05:58,568 - WARNING - 未找到候选文件: frame62_from_02538_NAT2021_train_0425bike1_13_0_BEST.mp4
2025-07-31 21:05:58,604 - INFO - 匹配成功: frame62_from_03625_WebUAV3M_train_bulk_carrier_8_1_BEST.mp4 -> frame62_from_03625_WebUAV3M_train_bulk_carrier_8_1_8_seed1.mp4
2025-07-31 21:05:58,632 - WARNING - 未找到候选文件: frame62_from_04152_WebUAV3M_train_coach_8_1_BEST.mp4
2025-07-31 21:05:58,666 - INFO - 匹配成功: frame62_from_06050_WebUAV3M_train_office_building_40_1_BEST.mp4 -> frame62_from_06050_WebUAV3M_train_office_building_40_1_3_seed1.mp4
2025-07-31 21:05:58,694 - WARNING - 未找到候选文件: frame63_from_00031_urbanvideo_train_BEST.mp4
2025-07-31 21:05:58,722 - WARNING - 未找到候选文件: frame63_from_04513_WebUAV3M_train_elephant_8_2_BEST.mp4
2025-07-31 21:05:58,757 - INFO - 匹配成功: frame63_from_04899_WebUAV3M_train_great_wall_of_china_2_2_BEST.mp4 -> frame63_from_04899_WebUAV3M_train_great_wall_of_china_2_2_5_seed1.mp4
2025-07-31 21:05:58,785 - WARNING - 未找到候选文件: frame64_from_00553_urbanvideo_train_BEST.mp4
2025-07-31 21:05:58,813 - WARNING - 未找到候选文件: frame64_from_02083_NAT2021_train_0264car5_3_0_BEST.mp4
2025-07-31 21:05:58,840 - WARNING - 未找到候选文件: frame64_from_02187_NAT2021_train_0404motor1_5_0_BEST.mp4
2025-07-31 21:05:58,868 - WARNING - 未找到候选文件: frame64_from_02356_NAT2021_train_0409building1_10_0_BEST.mp4
2025-07-31 21:05:58,904 - INFO - 匹配成功: frame64_from_03814_WebUAV3M_train_carrying_26_3_BEST.mp4 -> frame64_from_03814_WebUAV3M_train_carrying_26_3_7_seed42.mp4
2025-07-31 21:05:58,932 - WARNING - 未找到候选文件: frame64_from_04450_WebUAV3M_train_dropside_truck_27_0_BEST.mp4
2025-07-31 21:05:58,959 - WARNING - 未找到候选文件: frame65_from_00213_urbanvideo_train_BEST.mp4
2025-07-31 21:05:58,987 - WARNING - 未找到候选文件: frame65_from_02020_NAT2021_train_0186building2_2_0_BEST.mp4
2025-07-31 21:05:59,015 - WARNING - 未找到候选文件: frame65_from_02039_NAT2021_train_0261building1_0_BEST.mp4
2025-07-31 21:05:59,042 - WARNING - 未找到候选文件: frame65_from_02689_NAT2021_train_0427runner4_3_0_BEST.mp4
2025-07-31 21:05:59,070 - WARNING - 未找到候选文件: frame65_from_03629_WebUAV3M_train_bulldozer_1_1_BEST.mp4
2025-07-31 21:05:59,111 - INFO - 匹配成功: frame68_from_06013_WebUAV3M_train_office_building_26_2_BEST.mp4 -> frame68_from_06013_WebUAV3M_train_office_building_26_2_8_seed1.mp4
2025-07-31 21:05:59,141 - INFO - 匹配成功: frame69_from_06159_WebUAV3M_train_office_building_82_2_BEST.mp4 -> frame69_from_06159_WebUAV3M_train_office_building_82_2_2_seed1.mp4
2025-07-31 21:05:59,169 - WARNING - 未找到候选文件: frame6_from_01546_NAT2021_train_0000car8_1_BEST.mp4
2025-07-31 21:05:59,196 - WARNING - 未找到候选文件: frame6_from_02218_NAT2021_train_0404person2_9_0_BEST.mp4
2025-07-31 21:05:59,224 - WARNING - 未找到候选文件: frame6_from_06143_WebUAV3M_train_office_building_78_1_BEST.mp4
2025-07-31 21:05:59,256 - INFO - 匹配成功: frame6_from_07272_WebUAV3M_train_riding_an_electric_bicycle_29_1_BEST.mp4 -> frame6_from_07272_WebUAV3M_train_riding_an_electric_bicycle_29_1_2_seed42.mp4
2025-07-31 21:05:59,284 - WARNING - 未找到候选文件: frame6_from_07297_WebUAV3M_train_riding_an_electric_bicycle_45_1_BEST.mp4
2025-07-31 21:05:59,311 - WARNING - 未找到候选文件: frame70_from_04006_WebUAV3M_train_chimney_5_4_BEST.mp4
2025-07-31 21:05:59,339 - WARNING - 未找到候选文件: frame70_from_04588_WebUAV3M_train_ferry_19_1_BEST.mp4
2025-07-31 21:05:59,367 - WARNING - 未找到候选文件: frame70_from_05180_WebUAV3M_train_hatchback_95_2_BEST.mp4
2025-07-31 21:05:59,395 - WARNING - 未找到候选文件: frame72_from_01522_NAT2021_train_0000car26_6_0_BEST.mp4
2025-07-31 21:05:59,428 - INFO - 匹配成功: frame72_from_02451_NAT2021_train_0421car11_8_0_BEST.mp4 -> frame72_from_02451_NAT2021_train_0421car11_8_0_8_seed42.mp4
2025-07-31 21:05:59,456 - WARNING - 未找到候选文件: frame72_from_02708_NAT2021_train_0427truck1_2_0_BEST.mp4
2025-07-31 21:05:59,485 - WARNING - 未找到候选文件: frame72_from_03557_WebUAV3M_train_bulk_carrier_24_2_BEST.mp4
2025-07-31 21:05:59,513 - WARNING - 未找到候选文件: frame72_from_03930_WebUAV3M_train_cell_tower_13_0_BEST.mp4
2025-07-31 21:05:59,541 - WARNING - 未找到候选文件: frame72_from_06152_WebUAV3M_train_office_building_80_1_BEST.mp4
2025-07-31 21:05:59,569 - WARNING - 未找到候选文件: frame73_from_00466_urbanvideo_train_BEST.mp4
2025-07-31 21:05:59,597 - WARNING - 未找到候选文件: frame73_from_01660_NAT2021_train_0130car1_12_0_BEST.mp4
2025-07-31 21:05:59,624 - WARNING - 未找到候选文件: frame73_from_02248_NAT2021_train_0408bus2_5_0_BEST.mp4
2025-07-31 21:05:59,652 - WARNING - 未找到候选文件: frame73_from_03705_WebUAV3M_train_cab_9_0_BEST.mp4
2025-07-31 21:05:59,680 - WARNING - 未找到候选文件: frame73_from_05226_WebUAV3M_train_high_speed_train_22_1_BEST.mp4
2025-07-31 21:05:59,722 - INFO - 匹配成功: frame73_from_06185_WebUAV3M_train_ox_1_0_BEST.mp4 -> frame73_from_06185_WebUAV3M_train_ox_1_0_8_seed42.mp4
2025-07-31 21:05:59,750 - WARNING - 未找到候选文件: frame73_from_07196_WebUAV3M_train_reading_3_0_BEST.mp4
2025-07-31 21:05:59,777 - WARNING - 未找到候选文件: frame74_from_00446_urbanvideo_train_BEST.mp4
2025-07-31 21:05:59,805 - WARNING - 未找到候选文件: frame74_from_02764_NAT2021_train_0428person3_5_0_BEST.mp4
2025-07-31 21:05:59,837 - INFO - 匹配成功: frame74_from_03564_WebUAV3M_train_bulk_carrier_29_1_BEST.mp4 -> frame74_from_03564_WebUAV3M_train_bulk_carrier_29_1_4_seed42.mp4
2025-07-31 21:05:59,865 - WARNING - 未找到候选文件: frame75_from_00293_urbanvideo_train_BEST.mp4
2025-07-31 21:05:59,894 - WARNING - 未找到候选文件: frame75_from_02238_NAT2021_train_0407building1_1_0_BEST.mp4
2025-07-31 21:05:59,921 - WARNING - 未找到候选文件: frame75_from_02364_NAT2021_train_0409building1_4_0_BEST.mp4
2025-07-31 21:05:59,949 - WARNING - 未找到候选文件: frame75_from_03568_WebUAV3M_train_bulk_carrier_3_1_BEST.mp4
2025-07-31 21:05:59,977 - WARNING - 未找到候选文件: frame75_from_04558_WebUAV3M_train_ferris_wheel_9_4_BEST.mp4
2025-07-31 21:06:00,004 - WARNING - 未找到候选文件: frame75_from_07171_WebUAV3M_train_reading_23_2_BEST.mp4
2025-07-31 21:06:00,033 - WARNING - 未找到候选文件: frame76_from_00787_urbanvideo_train_BEST.mp4
2025-07-31 21:06:00,060 - WARNING - 未找到候选文件: frame76_from_01856_NAT2021_train_0138person18_4_0_BEST.mp4
2025-07-31 21:06:00,092 - INFO - 匹配成功: frame76_from_04221_WebUAV3M_train_container_truck_22_2_BEST.mp4 -> frame76_from_04221_WebUAV3M_train_container_truck_22_2_1_seed1.mp4
2025-07-31 21:06:00,122 - INFO - 匹配成功: frame79_from_02006_NAT2021_train_0184car2_5_0_BEST.mp4 -> frame79_from_02006_NAT2021_train_0184car2_5_0_4_seed42.mp4
2025-07-31 21:06:00,154 - INFO - 匹配成功: frame79_from_02369_NAT2021_train_0409building1_9_0_BEST.mp4 -> frame79_from_02369_NAT2021_train_0409building1_9_0_5_seed1.mp4
2025-07-31 21:06:00,182 - WARNING - 未找到候选文件: frame79_from_03563_WebUAV3M_train_bulk_carrier_29_0_BEST.mp4
2025-07-31 21:06:00,210 - WARNING - 未找到候选文件: frame79_from_04581_WebUAV3M_train_ferry_14_1_BEST.mp4
2025-07-31 21:06:00,238 - WARNING - 未找到候选文件: frame7_from_03979_WebUAV3M_train_chimney_17_7_BEST.mp4
2025-07-31 21:06:00,268 - WARNING - 未找到候选文件: frame7_from_04515_WebUAV3M_train_elevator_1_0_BEST.mp4
2025-07-31 21:06:00,296 - WARNING - 未找到候选文件: frame80_from_00309_urbanvideo_train_BEST.mp4
2025-07-31 21:06:00,324 - WARNING - 未找到候选文件: frame80_from_01920_NAT2021_train_0139car15_2_0_BEST.mp4
2025-07-31 21:06:00,352 - WARNING - 未找到候选文件: frame80_from_06112_WebUAV3M_train_office_building_66_0_BEST.mp4
2025-07-31 21:06:00,382 - INFO - 匹配成功: frame83_from_00556_urbanvideo_train_BEST.mp4 -> frame83_from_00556_urbanvideo_train_2_seed1.mp4
2025-07-31 21:06:00,417 - INFO - 匹配成功: frame83_from_03667_WebUAV3M_train_cab_18_1_BEST.mp4 -> frame83_from_03667_WebUAV3M_train_cab_18_1_3_seed1.mp4
2025-07-31 21:06:00,417 - INFO - 处理完成。成功匹配: 138, 无匹配: 213
2025-07-31 21:06:00,419 - INFO - 结果已保存到 best.csv
2025-07-31 21:08:32,465 - INFO - 开始处理 351 个最佳视频文件
2025-07-31 21:08:32,541 - WARNING - 匹配失败: frame0_from_02761_NAT2021_train_0428person3_2_0_BEST.mp4
2025-07-31 21:08:32,585 - INFO - 匹配成功: frame0_from_04991_WebUAV3M_train_harvester_41_2_BEST.mp4 -> frame0_from_04991_WebUAV3M_train_harvester_41_2_4_seed1.mp4
2025-07-31 21:08:32,639 - WARNING - 未找到候选文件: frame0_from_05981_WebUAV3M_train_office_building_15_3_BEST.mp4
2025-07-31 21:08:32,680 - INFO - 匹配成功: frame100_from_00143_urbanvideo_train_BEST.mp4 -> frame100_from_00143_urbanvideo_train_3_results_seed42.mp4
2025-07-31 21:08:32,712 - INFO - 匹配成功: frame100_from_02217_NAT2021_train_0404person2_8_0_BEST.mp4 -> frame100_from_02217_NAT2021_train_0404person2_8_0_4_results_seed1.mp4
2025-07-31 21:08:32,754 - INFO - 匹配成功: frame100_from_03809_WebUAV3M_train_carrying_25_1_BEST.mp4 -> frame100_from_03809_WebUAV3M_train_carrying_25_1_8_seed42.mp4
2025-07-31 21:08:32,808 - WARNING - 未找到候选文件: frame100_from_03973_WebUAV3M_train_chimney_17_1_BEST.mp4
2025-07-31 21:08:32,865 - WARNING - 未找到候选文件: frame100_from_04541_WebUAV3M_train_ferris_wheel_2_3_BEST.mp4
2025-07-31 21:08:32,894 - INFO - 匹配成功: frame101_from_00605_urbanvideo_train_BEST.mp4 -> frame101_from_00605_urbanvideo_train_1_results_seed1.mp4
2025-07-31 21:08:32,949 - WARNING - 未找到候选文件: frame101_from_00757_urbanvideo_train_BEST.mp4
2025-07-31 21:08:33,004 - WARNING - 未找到候选文件: frame101_from_01589_NAT2021_train_0000person6_1_0_BEST.mp4
2025-07-31 21:08:33,032 - INFO - 匹配成功: frame101_from_02574_NAT2021_train_0425car1_4_0_BEST.mp4 -> frame101_from_02574_NAT2021_train_0425car1_4_0_1_results_seed1.mp4
2025-07-31 21:08:33,066 - INFO - 匹配成功: frame101_from_02832_WebUAV3M_train_articulated_bus_5_0_BEST.mp4 -> frame101_from_02832_WebUAV3M_train_articulated_bus_5_0_2_results_seed42.mp4
2025-07-31 21:08:33,121 - WARNING - 未找到候选文件: frame101_from_04616_WebUAV3M_train_ferry_4_0_BEST.mp4
2025-07-31 21:08:33,151 - INFO - 匹配成功: frame101_from_05142_WebUAV3M_train_hatchback_75_1_BEST.mp4 -> frame101_from_05142_WebUAV3M_train_hatchback_75_1_1_seed1.mp4
2025-07-31 21:08:33,206 - WARNING - 未找到候选文件: frame101_from_07282_WebUAV3M_train_riding_an_electric_bicycle_36_0_BEST.mp4
2025-07-31 21:08:33,262 - WARNING - 未找到候选文件: frame102_from_00098_urbanvideo_train_BEST.mp4
2025-07-31 21:08:33,300 - INFO - 匹配成功: frame102_from_00305_urbanvideo_train_BEST.mp4 -> frame102_from_00305_urbanvideo_train_3_results_seed1.mp4
2025-07-31 21:08:33,344 - INFO - 匹配成功: frame102_from_04576_WebUAV3M_train_ferry_12_0_BEST.mp4 -> frame102_from_04576_WebUAV3M_train_ferry_12_0_8_results_seed1.mp4
2025-07-31 21:08:33,374 - INFO - 匹配成功: frame103_from_01678_NAT2021_train_0130car3_2_0_BEST.mp4 -> frame103_from_01678_NAT2021_train_0130car3_2_0_3_results_seed1.mp4
2025-07-31 21:08:33,428 - WARNING - 未找到候选文件: frame103_from_03805_WebUAV3M_train_carrying_24_0_BEST.mp4
2025-07-31 21:08:33,464 - INFO - 匹配成功: frame103_from_04636_WebUAV3M_train_fishing_boat_1_2_BEST.mp4 -> frame103_from_04636_WebUAV3M_train_fishing_boat_1_2_5_results_seed1.mp4
2025-07-31 21:08:33,511 - INFO - 匹配成功: frame103_from_06295_WebUAV3M_train_pagoda_28_4_BEST.mp4 -> frame103_from_06295_WebUAV3M_train_pagoda_28_4_6_results_seed42.mp4
2025-07-31 21:08:33,567 - WARNING - 未找到候选文件: frame103_from_07116_WebUAV3M_train_rapid_transit_4_1_BEST.mp4
2025-07-31 21:08:33,607 - INFO - 匹配成功: frame104_from_00055_urbanvideo_train_BEST.mp4 -> frame104_from_00055_urbanvideo_train_6_seed42.mp4
2025-07-31 21:08:33,678 - WARNING - 匹配失败: frame104_from_00604_urbanvideo_train_BEST.mp4
2025-07-31 21:08:33,708 - INFO - 匹配成功: frame104_from_00695_urbanvideo_train_BEST.mp4 -> frame104_from_00695_urbanvideo_train_2_seed42.mp4
2025-07-31 21:08:33,761 - WARNING - 未找到候选文件: frame104_from_03901_WebUAV3M_train_catamaran_3_1_BEST.mp4
2025-07-31 21:08:33,789 - INFO - 匹配成功: frame104_from_04175_WebUAV3M_train_container_ship_3_2_BEST.mp4 -> frame104_from_04175_WebUAV3M_train_container_ship_3_2_1_results_seed42.mp4
2025-07-31 21:08:33,824 - INFO - 匹配成功: frame104_from_06211_WebUAV3M_train_pagoda_10_5_BEST.mp4 -> frame104_from_06211_WebUAV3M_train_pagoda_10_5_5_seed1.mp4
2025-07-31 21:08:33,877 - INFO - 匹配成功: frame105_from_02824_WebUAV3M_train_apartment_8_0_BEST.mp4 -> frame105_from_02824_WebUAV3M_train_apartment_8_0_7_results_seed42.mp4
2025-07-31 21:08:33,915 - INFO - 匹配成功: frame105_from_04388_WebUAV3M_train_dog_4_1_BEST.mp4 -> frame105_from_04388_WebUAV3M_train_dog_4_1_4_seed1.mp4
2025-07-31 21:08:33,969 - WARNING - 未找到候选文件: frame106_from_04916_WebUAV3M_train_great_wall_of_china_7_1_BEST.mp4
2025-07-31 21:08:34,024 - WARNING - 未找到候选文件: frame106_from_07206_WebUAV3M_train_reading_6_1_BEST.mp4
2025-07-31 21:08:34,077 - WARNING - 未找到候选文件: frame106_from_07210_WebUAV3M_train_reading_7_1_BEST.mp4
2025-07-31 21:08:34,131 - WARNING - 未找到候选文件: frame106_from_07311_WebUAV3M_train_riding_an_electric_bicycle_57_0_BEST.mp4
2025-07-31 21:08:34,184 - WARNING - 未找到候选文件: frame107_from_02461_NAT2021_train_0421car3_2_0_BEST.mp4
2025-07-31 21:08:34,238 - WARNING - 未找到候选文件: frame107_from_04792_WebUAV3M_train_going_down_stairs_3_1_BEST.mp4
2025-07-31 21:08:34,293 - WARNING - 未找到候选文件: frame107_from_06119_WebUAV3M_train_office_building_68_1_BEST.mp4
2025-07-31 21:08:34,346 - WARNING - 未找到候选文件: frame108_from_02000_NAT2021_train_0184car1_0_BEST.mp4
2025-07-31 21:08:34,407 - WARNING - 匹配失败: frame108_from_03589_WebUAV3M_train_bulk_carrier_40_0_BEST.mp4
2025-07-31 21:08:34,438 - INFO - 匹配成功: frame108_from_03642_WebUAV3M_train_cable_car_3_0_BEST.mp4 -> frame108_from_03642_WebUAV3M_train_cable_car_3_0_1_seed1.mp4
2025-07-31 21:08:34,491 - WARNING - 未找到候选文件: frame108_from_07070_WebUAV3M_train_racing_dinghy_9_0_BEST.mp4
2025-07-31 21:08:34,528 - INFO - 匹配成功: frame109_from_00666_urbanvideo_train_BEST.mp4 -> frame109_from_00666_urbanvideo_train_3_results_seed42.mp4
2025-07-31 21:08:34,558 - INFO - 匹配成功: frame109_from_00745_urbanvideo_train_BEST.mp4 -> frame109_from_00745_urbanvideo_train_4_results_seed1.mp4
2025-07-31 21:08:34,612 - WARNING - 未找到候选文件: frame109_from_01453_NAT2021_train_0000car19_4_0_BEST.mp4
2025-07-31 21:08:34,666 - WARNING - 未找到候选文件: frame109_from_01593_NAT2021_train_0000person6_13_0_BEST.mp4
2025-07-31 21:08:34,721 - WARNING - 未找到候选文件: frame109_from_03730_WebUAV3M_train_calling_8_2_BEST.mp4
2025-07-31 21:08:34,774 - WARNING - 未找到候选文件: frame10_from_01807_NAT2021_train_0138car13_6_0_BEST.mp4
2025-07-31 21:08:34,828 - WARNING - 未找到候选文件: frame110_from_01790_NAT2021_train_0137person1_4_0_BEST.mp4
2025-07-31 21:08:34,881 - WARNING - 未找到候选文件: frame110_from_01852_NAT2021_train_0138person17_4_0_BEST.mp4
2025-07-31 21:08:34,935 - WARNING - 未找到候选文件: frame110_from_04186_WebUAV3M_train_container_ship_9_1_BEST.mp4
2025-07-31 21:08:34,989 - WARNING - 未找到候选文件: frame111_from_02183_NAT2021_train_0404motor1_17_0_BEST.mp4
2025-07-31 21:08:35,037 - INFO - 匹配成功: frame111_from_02936_WebUAV3M_train_beacon_26_1_BEST.mp4 -> frame111_from_02936_WebUAV3M_train_beacon_26_1_8_results_seed42.mp4
2025-07-31 21:08:35,071 - INFO - 匹配成功: frame112_from_02684_NAT2021_train_0427runner2_3_0_BEST.mp4 -> frame112_from_02684_NAT2021_train_0427runner2_3_0_4_results_seed1.mp4
2025-07-31 21:08:35,104 - INFO - 匹配成功: frame112_from_04551_WebUAV3M_train_ferris_wheel_6_0_BEST.mp4 -> frame112_from_04551_WebUAV3M_train_ferris_wheel_6_0_2_seed1.mp4
2025-07-31 21:08:35,135 - INFO - 匹配成功: frame113_from_02460_NAT2021_train_0421car3_15_0_BEST.mp4 -> frame113_from_02460_NAT2021_train_0421car3_15_0_4_results_seed42.mp4
2025-07-31 21:08:35,164 - INFO - 匹配成功: frame114_from_02509_NAT2021_train_0421truck1_3_0_BEST.mp4 -> frame114_from_02509_NAT2021_train_0421truck1_3_0_3_results_seed42.mp4
2025-07-31 21:08:35,219 - WARNING - 未找到候选文件: frame114_from_06228_WebUAV3M_train_pagoda_16_2_BEST.mp4
2025-07-31 21:08:35,273 - WARNING - 未找到候选文件: frame114_from_07023_WebUAV3M_train_pushing_a_cart_6_3_BEST.mp4
2025-07-31 21:08:35,327 - WARNING - 未找到候选文件: frame115_from_00074_urbanvideo_train_BEST.mp4
2025-07-31 21:08:35,380 - WARNING - 未找到候选文件: frame115_from_01551_NAT2021_train_0000car9_5_0_BEST.mp4
2025-07-31 21:08:35,434 - WARNING - 未找到候选文件: frame115_from_03848_WebUAV3M_train_carrying_31_1_BEST.mp4
2025-07-31 21:08:35,488 - WARNING - 未找到候选文件: frame115_from_03965_WebUAV3M_train_chimney_13_6_BEST.mp4
2025-07-31 21:08:35,542 - WARNING - 未找到候选文件: frame115_from_04249_WebUAV3M_train_cow_7_1_BEST.mp4
2025-07-31 21:08:35,595 - WARNING - 未找到候选文件: frame116_from_00515_urbanvideo_train_BEST.mp4
2025-07-31 21:08:35,625 - INFO - 匹配成功: frame116_from_03645_WebUAV3M_train_cable_car_6_1_BEST.mp4 -> frame116_from_03645_WebUAV3M_train_cable_car_6_1_3_seed42.mp4
2025-07-31 21:08:35,679 - WARNING - 未找到候选文件: frame116_from_04194_WebUAV3M_train_container_truck_10_0_BEST.mp4
2025-07-31 21:08:35,732 - WARNING - 未找到候选文件: frame116_from_05993_WebUAV3M_train_office_building_19_2_BEST.mp4
2025-07-31 21:08:35,785 - WARNING - 未找到候选文件: frame116_from_06015_WebUAV3M_train_office_building_28_0_BEST.mp4
2025-07-31 21:08:35,840 - WARNING - 未找到候选文件: frame117_from_00550_urbanvideo_train_BEST.mp4
2025-07-31 21:08:35,893 - WARNING - 未找到候选文件: frame117_from_01958_NAT2021_train_0173building3_5_0_BEST.mp4
2025-07-31 21:08:35,923 - INFO - 匹配成功: frame117_from_06028_WebUAV3M_train_office_building_32_2_BEST.mp4 -> frame117_from_06028_WebUAV3M_train_office_building_32_2_1_seed42.mp4
2025-07-31 21:08:35,956 - INFO - 匹配成功: frame117_from_07043_WebUAV3M_train_pylon_1_4_BEST.mp4 -> frame117_from_07043_WebUAV3M_train_pylon_1_4_4_seed1.mp4
2025-07-31 21:08:35,987 - INFO - 匹配成功: frame118_from_02530_NAT2021_train_0421truck2_6_0_BEST.mp4 -> frame118_from_02530_NAT2021_train_0421truck2_6_0_4_results_seed42.mp4
2025-07-31 21:08:36,039 - WARNING - 未找到候选文件: frame118_from_04965_WebUAV3M_train_harvester_38_3_BEST.mp4
2025-07-31 21:08:36,068 - INFO - 匹配成功: frame118_from_05968_WebUAV3M_train_office_building_10_0_BEST.mp4 -> frame118_from_05968_WebUAV3M_train_office_building_10_0_1_results_seed42.mp4
2025-07-31 21:08:36,097 - INFO - 匹配成功: frame118_from_07271_WebUAV3M_train_riding_an_electric_bicycle_29_0_BEST.mp4 -> frame118_from_07271_WebUAV3M_train_riding_an_electric_bicycle_29_0_1_seed1.mp4
2025-07-31 21:08:36,168 - WARNING - 匹配失败: frame118_from_07277_WebUAV3M_train_riding_an_electric_bicycle_32_0_BEST.mp4
2025-07-31 21:08:36,222 - WARNING - 未找到候选文件: frame119_from_01894_NAT2021_train_0138person5_5_0_BEST.mp4
2025-07-31 21:08:36,275 - WARNING - 未找到候选文件: frame119_from_02523_NAT2021_train_0421truck2_20_0_BEST.mp4
2025-07-31 21:08:36,329 - WARNING - 未找到候选文件: frame119_from_03610_WebUAV3M_train_bulk_carrier_5_1_BEST.mp4
2025-07-31 21:08:36,383 - WARNING - 未找到候选文件: frame119_from_06229_WebUAV3M_train_pagoda_16_3_BEST.mp4
2025-07-31 21:08:36,437 - WARNING - 未找到候选文件: frame119_from_07250_WebUAV3M_train_riding_an_electric_bicycle_1_0_BEST.mp4
2025-07-31 21:08:36,492 - WARNING - 未找到候选文件: frame11_from_03729_WebUAV3M_train_calling_8_1_BEST.mp4
2025-07-31 21:08:36,547 - WARNING - 未找到候选文件: frame11_from_04507_WebUAV3M_train_elephant_3_0_BEST.mp4
2025-07-31 21:08:36,601 - WARNING - 未找到候选文件: frame11_from_04571_WebUAV3M_train_ferry_1_1_BEST.mp4
2025-07-31 21:08:36,654 - WARNING - 未找到候选文件: frame120_from_01564_NAT2021_train_0000leaf1_0_BEST.mp4
2025-07-31 21:08:36,708 - WARNING - 未找到候选文件: frame120_from_03755_WebUAV3M_train_carrying_12_2_BEST.mp4
2025-07-31 21:08:36,737 - INFO - 匹配成功: frame120_from_04625_WebUAV3M_train_ferry_7_2_BEST.mp4 -> frame120_from_04625_WebUAV3M_train_ferry_7_2_3_seed1.mp4
2025-07-31 21:08:36,791 - WARNING - 未找到候选文件: frame120_from_07242_WebUAV3M_train_remote_control_boat_1_4_BEST.mp4
2025-07-31 21:08:36,843 - WARNING - 未找到候选文件: frame121_from_00028_urbanvideo_train_BEST.mp4
2025-07-31 21:08:36,898 - WARNING - 未找到候选文件: frame121_from_04407_WebUAV3M_train_dolphin_4_0_BEST.mp4
2025-07-31 21:08:36,927 - INFO - 匹配成功: frame122_from_00191_urbanvideo_train_BEST.mp4 -> frame122_from_00191_urbanvideo_train_1_seed42.mp4
2025-07-31 21:08:36,981 - WARNING - 未找到候选文件: frame122_from_02219_NAT2021_train_0404person3_1_0_BEST.mp4
2025-07-31 21:08:37,015 - INFO - 匹配成功: frame122_from_06167_WebUAV3M_train_oil_tanker_1_1_BEST.mp4 -> frame122_from_06167_WebUAV3M_train_oil_tanker_1_1_3_results_seed1.mp4
2025-07-31 21:08:37,068 - WARNING - 未找到候选文件: frame123_from_03649_WebUAV3M_train_cable_car_8_1_BEST.mp4
2025-07-31 21:08:37,108 - INFO - 匹配成功: frame123_from_03768_WebUAV3M_train_carrying_14_5_BEST.mp4 -> frame123_from_03768_WebUAV3M_train_carrying_14_5_7_results_seed42.mp4
2025-07-31 21:08:37,163 - WARNING - 未找到候选文件: frame123_from_03867_WebUAV3M_train_carrying_37_0_BEST.mp4
2025-07-31 21:08:37,198 - INFO - 匹配成功: frame123_from_03892_WebUAV3M_train_castle_2_1_BEST.mp4 -> frame123_from_03892_WebUAV3M_train_castle_2_1_5_results_seed42.mp4
2025-07-31 21:08:37,250 - WARNING - 未找到候选文件: frame123_from_04627_WebUAV3M_train_ferry_8_1_BEST.mp4
2025-07-31 21:08:37,304 - WARNING - 未找到候选文件: frame123_from_07113_WebUAV3M_train_rapid_transit_3_4_BEST.mp4
2025-07-31 21:08:37,358 - WARNING - 未找到候选文件: frame124_from_00441_urbanvideo_train_BEST.mp4
2025-07-31 21:08:37,412 - WARNING - 未找到候选文件: frame124_from_01998_NAT2021_train_0175car1_2_0_BEST.mp4
2025-07-31 21:08:37,466 - WARNING - 未找到候选文件: frame124_from_04449_WebUAV3M_train_dropside_truck_26_0_BEST.mp4
2025-07-31 21:08:37,494 - INFO - 匹配成功: frame124_from_07156_WebUAV3M_train_reading_19_1_BEST.mp4 -> frame124_from_07156_WebUAV3M_train_reading_19_1_1_seed1.mp4
2025-07-31 21:08:37,549 - WARNING - 未找到候选文件: frame125_from_00403_urbanvideo_train_BEST.mp4
2025-07-31 21:08:37,587 - INFO - 匹配成功: frame125_from_00585_urbanvideo_train_BEST.mp4 -> frame125_from_00585_urbanvideo_train_7_seed1.mp4
2025-07-31 21:08:37,641 - WARNING - 未找到候选文件: frame125_from_03668_WebUAV3M_train_cab_19_0_BEST.mp4
2025-07-31 21:08:37,696 - WARNING - 未找到候选文件: frame125_from_03825_WebUAV3M_train_carrying_28_5_BEST.mp4
2025-07-31 21:08:37,749 - WARNING - 未找到候选文件: frame125_from_04216_WebUAV3M_train_container_truck_21_0_BEST.mp4
2025-07-31 21:08:37,830 - WARNING - 匹配失败: frame125_from_04677_WebUAV3M_train_fox_1_0_BEST.mp4
2025-07-31 21:08:37,867 - INFO - 匹配成功: frame125_from_05008_WebUAV3M_train_harvester_46_0_BEST.mp4 -> frame125_from_05008_WebUAV3M_train_harvester_46_0_3_seed1.mp4
2025-07-31 21:08:37,920 - WARNING - 未找到候选文件: frame126_from_00239_urbanvideo_train_BEST.mp4
2025-07-31 21:08:37,974 - WARNING - 未找到候选文件: frame126_from_00273_urbanvideo_train_BEST.mp4
2025-07-31 21:08:38,027 - WARNING - 未找到候选文件: frame126_from_00285_urbanvideo_train_BEST.mp4
2025-07-31 21:08:38,081 - WARNING - 未找到候选文件: frame126_from_02210_NAT2021_train_0404person2_10_0_BEST.mp4
2025-07-31 21:08:38,136 - WARNING - 未找到候选文件: frame126_from_02261_NAT2021_train_0408car2_0_BEST.mp4
2025-07-31 21:08:38,169 - INFO - 匹配成功: frame126_from_06129_WebUAV3M_train_office_building_71_2_BEST.mp4 -> frame126_from_06129_WebUAV3M_train_office_building_71_2_5_seed1.mp4
2025-07-31 21:08:38,203 - INFO - 匹配成功: frame127_from_02673_NAT2021_train_0427runner1_15_0_BEST.mp4 -> frame127_from_02673_NAT2021_train_0427runner1_15_0_6_seed1.mp4
2025-07-31 21:08:38,256 - WARNING - 未找到候选文件: frame127_from_04253_WebUAV3M_train_cow_9_0_BEST.mp4
2025-07-31 21:08:38,285 - INFO - 匹配成功: frame127_from_04417_WebUAV3M_train_double-decker_bus_1_0_BEST.mp4 -> frame127_from_04417_WebUAV3M_train_double-decker_bus_1_0_1_seed42.mp4
2025-07-31 21:08:38,338 - WARNING - 未找到候选文件: frame128_from_04421_WebUAV3M_train_double-decker_bus_4_0_BEST.mp4
2025-07-31 21:08:38,392 - WARNING - 未找到候选文件: frame128_from_04630_WebUAV3M_train_filming_1_0_BEST.mp4
2025-07-31 21:08:38,457 - WARNING - 匹配失败: frame12_from_00334_urbanvideo_train_BEST.mp4
2025-07-31 21:08:38,511 - WARNING - 未找到候选文件: frame12_from_00637_urbanvideo_train_BEST.mp4
2025-07-31 21:08:38,541 - INFO - 匹配成功: frame12_from_06283_WebUAV3M_train_pagoda_26_7_BEST.mp4 -> frame12_from_06283_WebUAV3M_train_pagoda_26_7_3_results_seed42.mp4
2025-07-31 21:08:38,594 - WARNING - 未找到候选文件: frame12_from_06297_WebUAV3M_train_pagoda_28_6_BEST.mp4
2025-07-31 21:08:38,628 - INFO - 匹配成功: frame13_from_01490_NAT2021_train_0000car23_15_0_BEST.mp4 -> frame13_from_01490_NAT2021_train_0000car23_15_0_8_seed42.mp4
2025-07-31 21:08:38,671 - INFO - 匹配成功: frame13_from_01716_NAT2021_train_0131ship1_10_0_BEST.mp4 -> frame13_from_01716_NAT2021_train_0131ship1_10_0_7_results_seed42.mp4
2025-07-31 21:08:38,724 - WARNING - 未找到候选文件: frame13_from_02111_NAT2021_train_0264person4_3_0_BEST.mp4
2025-07-31 21:08:38,778 - WARNING - 未找到候选文件: frame13_from_04438_WebUAV3M_train_dropside_truck_20_1_BEST.mp4
2025-07-31 21:08:38,832 - WARNING - 未找到候选文件: frame14_from_01650_NAT2021_train_0000sign4_0_BEST.mp4
2025-07-31 21:08:38,895 - WARNING - 匹配失败: frame14_from_02924_WebUAV3M_train_beacon_21_1_BEST.mp4
2025-07-31 21:08:38,946 - INFO - 匹配成功: frame14_from_04328_WebUAV3M_train_digging_bucket_14_2_BEST.mp4 -> frame14_from_04328_WebUAV3M_train_digging_bucket_14_2_8_seed42.mp4
2025-07-31 21:08:38,977 - INFO - 匹配成功: frame15_from_07213_WebUAV3M_train_reading_8_0_BEST.mp4 -> frame15_from_07213_WebUAV3M_train_reading_8_0_3_seed1.mp4
2025-07-31 21:08:39,030 - WARNING - 未找到候选文件: frame15_from_07266_WebUAV3M_train_riding_an_electric_bicycle_24_1_BEST.mp4
2025-07-31 21:08:39,061 - INFO - 匹配成功: frame16_from_02015_NAT2021_train_0186building1_1_0_BEST.mp4 -> frame16_from_02015_NAT2021_train_0186building1_1_0_3_seed42.mp4
2025-07-31 21:08:39,115 - WARNING - 未找到候选文件: frame16_from_03974_WebUAV3M_train_chimney_17_2_BEST.mp4
2025-07-31 21:08:39,170 - WARNING - 未找到候选文件: frame16_from_04466_WebUAV3M_train_dropside_truck_34_0_BEST.mp4
2025-07-31 21:08:39,199 - INFO - 匹配成功: frame17_from_02404_NAT2021_train_0420car1_5_0_BEST.mp4 -> frame17_from_02404_NAT2021_train_0420car1_5_0_3_seed42.mp4
2025-07-31 21:08:39,229 - INFO - 匹配成功: frame17_from_02699_NAT2021_train_0427runner6_3_0_BEST.mp4 -> frame17_from_02699_NAT2021_train_0427runner6_3_0_4_seed1.mp4
2025-07-31 21:08:39,282 - WARNING - 未找到候选文件: frame17_from_02799_WebUAV3M_train_apartment_12_0_BEST.mp4
2025-07-31 21:08:39,319 - INFO - 匹配成功: frame17_from_03394_WebUAV3M_train_box_truck_13_1_BEST.mp4 -> frame17_from_03394_WebUAV3M_train_box_truck_13_1_5_seed1.mp4
2025-07-31 21:08:39,371 - WARNING - 未找到候选文件: frame17_from_04601_WebUAV3M_train_ferry_22_1_BEST.mp4
2025-07-31 21:08:39,403 - INFO - 匹配成功: frame17_from_05166_WebUAV3M_train_hatchback_90_1_BEST.mp4 -> frame17_from_05166_WebUAV3M_train_hatchback_90_1_2_seed1.mp4
2025-07-31 21:08:39,458 - WARNING - 未找到候选文件: frame18_from_00693_urbanvideo_train_BEST.mp4
2025-07-31 21:08:39,489 - INFO - 匹配成功: frame18_from_02242_NAT2021_train_0408bus1_1_0_BEST.mp4 -> frame18_from_02242_NAT2021_train_0408bus1_1_0_3_results_seed1.mp4
2025-07-31 21:08:39,541 - WARNING - 未找到候选文件: frame18_from_02847_WebUAV3M_train_bamboo_raft_5_0_BEST.mp4
2025-07-31 21:08:39,607 - INFO - 匹配成功: frame18_from_03455_WebUAV3M_train_box_truck_48_0_BEST.mp4 -> frame18_from_03455_WebUAV3M_train_box_truck_48_0_8_results_seed42.mp4
2025-07-31 21:08:39,660 - WARNING - 未找到候选文件: frame18_from_05092_WebUAV3M_train_hatchback_29_1_BEST.mp4
2025-07-31 21:08:39,713 - WARNING - 未找到候选文件: frame19_from_04233_WebUAV3M_train_cow_10_0_BEST.mp4
2025-07-31 21:08:39,742 - INFO - 匹配成功: frame19_from_06200_WebUAV3M_train_ox_9_0_BEST.mp4 -> frame19_from_06200_WebUAV3M_train_ox_9_0_1_results_seed42.mp4
2025-07-31 21:08:39,796 - WARNING - 未找到候选文件: frame1_from_00367_urbanvideo_train_BEST.mp4
2025-07-31 21:08:39,860 - WARNING - 匹配失败: frame1_from_02804_WebUAV3M_train_apartment_15_0_BEST.mp4
2025-07-31 21:08:39,889 - INFO - 匹配成功: frame1_from_02849_WebUAV3M_train_bamboo_raft_6_0_BEST.mp4 -> frame1_from_02849_WebUAV3M_train_bamboo_raft_6_0_1_seed42.mp4
2025-07-31 21:08:39,942 - WARNING - 未找到候选文件: frame1_from_03669_WebUAV3M_train_cab_2_0_BEST.mp4
2025-07-31 21:08:39,985 - INFO - 匹配成功: frame1_from_03955_WebUAV3M_train_chimney_1_8_BEST.mp4 -> frame1_from_03955_WebUAV3M_train_chimney_1_8_8_seed1.mp4
2025-07-31 21:08:40,038 - WARNING - 未找到候选文件: frame21_from_00003_urbanvideo_train_BEST.mp4
2025-07-31 21:08:40,092 - WARNING - 未找到候选文件: frame21_from_03652_WebUAV3M_train_cable_car_8_4_BEST.mp4
2025-07-31 21:08:40,122 - INFO - 匹配成功: frame22_from_01759_NAT2021_train_0133car2_6_0_BEST.mp4 -> frame22_from_01759_NAT2021_train_0133car2_6_0_2_results_seed42.mp4
2025-07-31 21:08:40,181 - WARNING - 匹配失败: frame22_from_02015_NAT2021_train_0186building1_1_0_BEST.mp4
2025-07-31 21:08:40,215 - INFO - 匹配成功: frame22_from_02846_WebUAV3M_train_bamboo_raft_4_1_BEST.mp4 -> frame22_from_02846_WebUAV3M_train_bamboo_raft_4_1_3_results_seed42.mp4
2025-07-31 21:08:40,244 - INFO - 匹配成功: frame22_from_05087_WebUAV3M_train_hatchback_23_1_BEST.mp4 -> frame22_from_05087_WebUAV3M_train_hatchback_23_1_1_seed42.mp4
2025-07-31 21:08:40,297 - WARNING - 未找到候选文件: frame22_from_06230_WebUAV3M_train_pagoda_16_4_BEST.mp4
2025-07-31 21:08:40,351 - WARNING - 未找到候选文件: frame23_from_00469_urbanvideo_train_BEST.mp4
2025-07-31 21:08:40,404 - WARNING - 未找到候选文件: frame23_from_01876_NAT2021_train_0138person4_15_0_BEST.mp4
2025-07-31 21:08:40,456 - WARNING - 未找到候选文件: frame23_from_02703_NAT2021_train_0427truck1_11_0_BEST.mp4
2025-07-31 21:08:40,486 - INFO - 匹配成功: frame23_from_02939_WebUAV3M_train_beacon_26_4_BEST.mp4 -> frame23_from_02939_WebUAV3M_train_beacon_26_4_1_seed1.mp4
2025-07-31 21:08:40,546 - WARNING - 匹配失败: frame23_from_03664_WebUAV3M_train_cab_16_2_BEST.mp4
2025-07-31 21:08:40,599 - WARNING - 未找到候选文件: frame25_from_00459_urbanvideo_train_BEST.mp4
2025-07-31 21:08:40,653 - WARNING - 未找到候选文件: frame25_from_00721_urbanvideo_train_BEST.mp4
2025-07-31 21:08:40,687 - INFO - 匹配成功: frame25_from_01772_NAT2021_train_0136car1_11_0_BEST.mp4 -> frame25_from_01772_NAT2021_train_0136car1_11_0_5_seed42.mp4
2025-07-31 21:08:40,731 - INFO - 匹配成功: frame25_from_07330_WebUAV3M_train_riding_an_electric_bicycle_9_1_BEST.mp4 -> frame25_from_07330_WebUAV3M_train_riding_an_electric_bicycle_9_1_3_results_seed42.mp4
2025-07-31 21:08:40,786 - INFO - 匹配成功: frame26_from_00222_urbanvideo_train_BEST.mp4 -> frame26_from_00222_urbanvideo_train_7_results_seed1.mp4
2025-07-31 21:08:40,819 - INFO - 匹配成功: frame26_from_00736_urbanvideo_train_BEST.mp4 -> frame26_from_00736_urbanvideo_train_2_results_seed1.mp4
2025-07-31 21:08:40,856 - INFO - 匹配成功: frame26_from_02326_NAT2021_train_0409bridge1_1_0_BEST.mp4 -> frame26_from_02326_NAT2021_train_0409bridge1_1_0_6_results_seed42.mp4
2025-07-31 21:08:40,888 - INFO - 匹配成功: frame26_from_07245_WebUAV3M_train_rice_transplanter_1_0_BEST.mp4 -> frame26_from_07245_WebUAV3M_train_rice_transplanter_1_0_2_seed42.mp4
2025-07-31 21:08:40,941 - WARNING - 未找到候选文件: frame27_from_02445_NAT2021_train_0421car11_2_0_BEST.mp4
2025-07-31 21:08:40,980 - INFO - 匹配成功: frame28_from_01930_NAT2021_train_0139car17_4_0_BEST.mp4 -> frame28_from_01930_NAT2021_train_0139car17_4_0_5_results_seed1.mp4
2025-07-31 21:08:41,008 - INFO - 匹配成功: frame29_from_01944_NAT2021_train_0173building1_2_0_BEST.mp4 -> frame29_from_01944_NAT2021_train_0173building1_2_0_3_seed1.mp4
2025-07-31 21:08:41,043 - INFO - 匹配成功: frame29_from_04406_WebUAV3M_train_dolphin_3_2_BEST.mp4 -> frame29_from_04406_WebUAV3M_train_dolphin_3_2_3_results_seed42.mp4
2025-07-31 21:08:41,076 - INFO - 匹配成功: frame2_from_02201_NAT2021_train_0404person1_2_0_BEST.mp4 -> frame2_from_02201_NAT2021_train_0404person1_2_0_7_results_seed1.mp4
2025-07-31 21:08:41,104 - INFO - 匹配成功: frame2_from_02402_NAT2021_train_0420car1_3_0_BEST.mp4 -> frame2_from_02402_NAT2021_train_0420car1_3_0_3_seed42.mp4
2025-07-31 21:08:41,131 - INFO - 匹配成功: frame2_from_02514_NAT2021_train_0421truck2_12_0_BEST.mp4 -> frame2_from_02514_NAT2021_train_0421truck2_12_0_2_seed42.mp4
2025-07-31 21:08:41,163 - INFO - 匹配成功: frame2_from_03760_WebUAV3M_train_carrying_13_3_BEST.mp4 -> frame2_from_03760_WebUAV3M_train_carrying_13_3_4_seed42.mp4
2025-07-31 21:08:41,217 - WARNING - 未找到候选文件: frame2_from_07247_WebUAV3M_train_rice_transplanter_1_2_BEST.mp4
2025-07-31 21:08:41,271 - WARNING - 未找到候选文件: frame30_from_02664_NAT2021_train_0427motor1_4_0_BEST.mp4
2025-07-31 21:08:41,347 - WARNING - 匹配失败: frame30_from_03650_WebUAV3M_train_cable_car_8_2_BEST.mp4
2025-07-31 21:08:41,399 - WARNING - 未找到候选文件: frame30_from_04009_WebUAV3M_train_chimney_5_7_BEST.mp4
2025-07-31 21:08:41,454 - WARNING - 未找到候选文件: frame31_from_02448_NAT2021_train_0421car11_5_0_BEST.mp4
2025-07-31 21:08:41,508 - WARNING - 未找到候选文件: frame31_from_05178_WebUAV3M_train_hatchback_95_0_BEST.mp4
2025-07-31 21:08:41,561 - WARNING - 未找到候选文件: frame31_from_06034_WebUAV3M_train_office_building_35_1_BEST.mp4
2025-07-31 21:08:41,615 - WARNING - 未找到候选文件: frame31_from_06249_WebUAV3M_train_pagoda_18_8_BEST.mp4
2025-07-31 21:08:41,669 - WARNING - 未找到候选文件: frame32_from_00701_urbanvideo_train_BEST.mp4
2025-07-31 21:08:41,722 - WARNING - 未找到候选文件: frame32_from_00755_urbanvideo_train_BEST.mp4
2025-07-31 21:08:41,758 - INFO - 匹配成功: frame32_from_02957_WebUAV3M_train_beacon_34_0_BEST.mp4 -> frame32_from_02957_WebUAV3M_train_beacon_34_0_6_results_seed1.mp4
2025-07-31 21:08:41,790 - INFO - 匹配成功: frame32_from_04234_WebUAV3M_train_cow_11_0_BEST.mp4 -> frame32_from_04234_WebUAV3M_train_cow_11_0_4_seed1.mp4
2025-07-31 21:08:41,835 - INFO - 匹配成功: frame32_from_04518_WebUAV3M_train_excavator_1_1_BEST.mp4 -> frame32_from_04518_WebUAV3M_train_excavator_1_1_7_results_seed1.mp4
2025-07-31 21:08:41,868 - INFO - 匹配成功: frame32_from_05923_WebUAV3M_train_motorboat_58_1_BEST.mp4 -> frame32_from_05923_WebUAV3M_train_motorboat_58_1_2_results_seed1.mp4
2025-07-31 21:08:41,897 - INFO - 匹配成功: frame32_from_06041_WebUAV3M_train_office_building_38_0_BEST.mp4 -> frame32_from_06041_WebUAV3M_train_office_building_38_0_2_seed1.mp4
2025-07-31 21:08:41,941 - INFO - 匹配成功: frame33_from_00141_urbanvideo_train_BEST.mp4 -> frame33_from_00141_urbanvideo_train_8_seed1.mp4
2025-07-31 21:08:41,987 - INFO - 匹配成功: frame33_from_01825_NAT2021_train_0138car9_6_0_BEST.mp4 -> frame33_from_01825_NAT2021_train_0138car9_6_0_5_results_seed1.mp4
2025-07-31 21:08:42,025 - INFO - 匹配成功: frame33_from_03988_WebUAV3M_train_chimney_19_3_BEST.mp4 -> frame33_from_03988_WebUAV3M_train_chimney_19_3_8_seed42.mp4
2025-07-31 21:08:42,059 - INFO - 匹配成功: frame34_from_03548_WebUAV3M_train_bulk_carrier_22_3_BEST.mp4 -> frame34_from_03548_WebUAV3M_train_bulk_carrier_22_3_8_seed1.mp4
2025-07-31 21:08:42,105 - INFO - 匹配成功: frame34_from_04517_WebUAV3M_train_excavator_1_0_BEST.mp4 -> frame34_from_04517_WebUAV3M_train_excavator_1_0_8_results_seed1.mp4
2025-07-31 21:08:42,137 - INFO - 匹配成功: frame34_from_07316_WebUAV3M_train_riding_an_electric_bicycle_6_0_BEST.mp4 -> frame34_from_07316_WebUAV3M_train_riding_an_electric_bicycle_6_0_2_seed1.mp4
2025-07-31 21:08:42,169 - INFO - 匹配成功: frame35_from_02268_NAT2021_train_0408car3_6_0_BEST.mp4 -> frame35_from_02268_NAT2021_train_0408car3_6_0_8_seed42.mp4
2025-07-31 21:08:42,201 - INFO - 匹配成功: frame35_from_05176_WebUAV3M_train_hatchback_94_1_BEST.mp4 -> frame35_from_05176_WebUAV3M_train_hatchback_94_1_2_results_seed1.mp4
2025-07-31 21:08:42,237 - INFO - 匹配成功: frame36_from_04939_WebUAV3M_train_harvester_25_0_BEST.mp4 -> frame36_from_04939_WebUAV3M_train_harvester_25_0_5_seed42.mp4
2025-07-31 21:08:42,266 - INFO - 匹配成功: frame37_from_00027_urbanvideo_train_BEST.mp4 -> frame37_from_00027_urbanvideo_train_1_seed1.mp4
2025-07-31 21:08:42,295 - INFO - 匹配成功: frame37_from_06251_WebUAV3M_train_pagoda_18_10_BEST.mp4 -> frame37_from_06251_WebUAV3M_train_pagoda_18_10_1_seed1.mp4
2025-07-31 21:08:42,328 - INFO - 匹配成功: frame38_from_04606_WebUAV3M_train_ferry_25_0_BEST.mp4 -> frame38_from_04606_WebUAV3M_train_ferry_25_0_5_results_seed42.mp4
2025-07-31 21:08:42,370 - INFO - 匹配成功: frame39_from_00700_urbanvideo_train_BEST.mp4 -> frame39_from_00700_urbanvideo_train_5_seed42.mp4
2025-07-31 21:08:42,403 - INFO - 匹配成功: frame3_from_00439_urbanvideo_train_BEST.mp4 -> frame3_from_00439_urbanvideo_train_4_results_seed42.mp4
2025-07-31 21:08:42,436 - INFO - 匹配成功: frame40_from_00169_urbanvideo_train_BEST.mp4 -> frame40_from_00169_urbanvideo_train_3_seed42.mp4
2025-07-31 21:08:42,479 - INFO - 匹配成功: frame40_from_00228_urbanvideo_train_BEST.mp4 -> frame40_from_00228_urbanvideo_train_4_results_seed1.mp4
2025-07-31 21:08:42,510 - INFO - 匹配成功: frame40_from_02968_WebUAV3M_train_beacon_9_0_BEST.mp4 -> frame40_from_02968_WebUAV3M_train_beacon_9_0_5_results_seed42.mp4
2025-07-31 21:08:42,549 - INFO - 匹配成功: frame40_from_03966_WebUAV3M_train_chimney_13_7_BEST.mp4 -> frame40_from_03966_WebUAV3M_train_chimney_13_7_5_results_seed42.mp4
2025-07-31 21:08:42,596 - INFO - 匹配成功: frame41_from_00477_urbanvideo_train_BEST.mp4 -> frame41_from_00477_urbanvideo_train_7_results_seed1.mp4
2025-07-31 21:08:42,644 - INFO - 匹配成功: frame43_from_06087_WebUAV3M_train_office_building_55_1_BEST.mp4 -> frame43_from_06087_WebUAV3M_train_office_building_55_1_7_results_seed42.mp4
2025-07-31 21:08:42,671 - INFO - 匹配成功: frame44_from_01602_NAT2021_train_0000person6_21_0_BEST.mp4 -> frame44_from_01602_NAT2021_train_0000person6_21_0_2_seed1.mp4
2025-07-31 21:08:42,700 - INFO - 匹配成功: frame45_from_01661_NAT2021_train_0130car1_13_0_BEST.mp4 -> frame45_from_01661_NAT2021_train_0130car1_13_0_1_seed42.mp4
2025-07-31 21:08:42,754 - WARNING - 未找到候选文件: frame46_from_06102_WebUAV3M_train_office_building_6_2_BEST.mp4
2025-07-31 21:08:42,807 - WARNING - 未找到候选文件: frame47_from_00419_urbanvideo_train_BEST.mp4
2025-07-31 21:08:42,860 - WARNING - 未找到候选文件: frame46_from_06121_WebUAV3M_train_office_building_69_0_BEST.mp4
2025-07-31 21:08:42,936 - WARNING - 匹配失败: frame47_from_03999_WebUAV3M_train_chimney_2_5_BEST.mp4
2025-07-31 21:08:42,989 - WARNING - 未找到候选文件: frame47_from_04538_WebUAV3M_train_ferris_wheel_2_0_BEST.mp4
2025-07-31 21:08:43,042 - INFO - 匹配成功: frame47_from_07342_WebUAV3M_train_riding_a_horse_4_6_BEST.mp4 -> frame47_from_07342_WebUAV3M_train_riding_a_horse_4_6_6_results_seed42.mp4
2025-07-31 21:08:43,069 - INFO - 匹配成功: frame48_from_01411_NAT2021_train_0000car13_2_0_BEST.mp4 -> frame48_from_01411_NAT2021_train_0000car13_2_0_1_seed42.mp4
2025-07-31 21:08:43,124 - WARNING - 未找到候选文件: frame48_from_02617_NAT2021_train_0426person3_8_0_BEST.mp4
2025-07-31 21:08:43,174 - INFO - 匹配成功: frame48_from_03650_WebUAV3M_train_cable_car_8_2_BEST.mp4 -> frame48_from_03650_WebUAV3M_train_cable_car_8_2_8_results_seed42.mp4
2025-07-31 21:08:43,210 - INFO - 匹配成功: frame48_from_04566_WebUAV3M_train_ferris_wheel_pod_2_0_BEST.mp4 -> frame48_from_04566_WebUAV3M_train_ferris_wheel_pod_2_0_4_results_seed42.mp4
2025-07-31 21:08:43,264 - WARNING - 未找到候选文件: frame48_from_05002_WebUAV3M_train_harvester_45_0_BEST.mp4
2025-07-31 21:08:43,324 - WARNING - 匹配失败: frame49_from_00556_urbanvideo_train_BEST.mp4
2025-07-31 21:08:43,377 - WARNING - 未找到候选文件: frame49_from_02101_NAT2021_train_0264person3_2_0_BEST.mp4
2025-07-31 21:08:43,405 - INFO - 匹配成功: frame49_from_02671_NAT2021_train_0427runner1_13_0_BEST.mp4 -> frame49_from_02671_NAT2021_train_0427runner1_13_0_1_results_seed1.mp4
2025-07-31 21:08:43,435 - INFO - 匹配成功: frame49_from_03915_WebUAV3M_train_catboat_1_4_BEST.mp4 -> frame49_from_03915_WebUAV3M_train_catboat_1_4_2_results_seed42.mp4
2025-07-31 21:08:43,488 - WARNING - 未找到候选文件: frame4_from_00122_urbanvideo_train_BEST.mp4
2025-07-31 21:08:43,541 - WARNING - 未找到候选文件: frame4_from_00775_urbanvideo_train_BEST.mp4
2025-07-31 21:08:43,590 - INFO - 匹配成功: frame4_from_02115_NAT2021_train_0264person5_3_0_BEST.mp4 -> frame4_from_02115_NAT2021_train_0264person5_3_0_7_results_seed42.mp4
2025-07-31 21:08:43,643 - WARNING - 未找到候选文件: frame4_from_03495_WebUAV3M_train_brown_bear_3_2_BEST.mp4
2025-07-31 21:08:43,689 - INFO - 匹配成功: frame4_from_04386_WebUAV3M_train_dog_3_7_BEST.mp4 -> frame4_from_04386_WebUAV3M_train_dog_3_7_3_results_seed1.mp4
2025-07-31 21:08:43,743 - WARNING - 未找到候选文件: frame4_from_06106_WebUAV3M_train_office_building_62_0_BEST.mp4
2025-07-31 21:08:43,777 - INFO - 匹配成功: frame4_from_06149_WebUAV3M_train_office_building_8_1_BEST.mp4 -> frame4_from_06149_WebUAV3M_train_office_building_8_1_5_results_seed1.mp4
2025-07-31 21:08:43,816 - INFO - 匹配成功: frame4_from_07267_WebUAV3M_train_riding_an_electric_bicycle_25_0_BEST.mp4 -> frame4_from_07267_WebUAV3M_train_riding_an_electric_bicycle_25_0_4_seed1.mp4
2025-07-31 21:08:43,871 - WARNING - 未找到候选文件: frame50_from_00323_urbanvideo_train_BEST.mp4
2025-07-31 21:08:43,923 - WARNING - 未找到候选文件: frame50_from_01632_NAT2021_train_0000rider4_9_0_BEST.mp4
2025-07-31 21:08:43,978 - WARNING - 未找到候选文件: frame50_from_02246_NAT2021_train_0408bus2_3_0_BEST.mp4
2025-07-31 21:08:44,030 - WARNING - 未找到候选文件: frame50_from_03823_WebUAV3M_train_carrying_28_3_BEST.mp4
2025-07-31 21:08:44,073 - INFO - 匹配成功: frame51_from_00724_urbanvideo_train_BEST.mp4 -> frame51_from_00724_urbanvideo_train_4_results_seed1.mp4
2025-07-31 21:08:44,103 - INFO - 匹配成功: frame51_from_02086_NAT2021_train_0264person1_2_0_BEST.mp4 -> frame51_from_02086_NAT2021_train_0264person1_2_0_4_seed42.mp4
2025-07-31 21:08:44,157 - WARNING - 未找到候选文件: frame51_from_02834_WebUAV3M_train_balloon_3_0_BEST.mp4
2025-07-31 21:08:44,189 - INFO - 匹配成功: frame51_from_03882_WebUAV3M_train_carrying_8_1_BEST.mp4 -> frame51_from_03882_WebUAV3M_train_carrying_8_1_7_seed1.mp4
2025-07-31 21:08:44,223 - INFO - 匹配成功: frame51_from_04935_WebUAV3M_train_harvester_21_0_BEST.mp4 -> frame51_from_04935_WebUAV3M_train_harvester_21_0_6_seed1.mp4
2025-07-31 21:08:44,291 - WARNING - 匹配失败: frame51_from_06050_WebUAV3M_train_office_building_40_1_BEST.mp4
2025-07-31 21:08:44,345 - WARNING - 未找到候选文件: frame51_from_06095_WebUAV3M_train_office_building_58_0_BEST.mp4
2025-07-31 21:08:44,399 - WARNING - 未找到候选文件: frame52_from_03480_WebUAV3M_train_box_truck_64_1_BEST.mp4
2025-07-31 21:08:44,442 - INFO - 匹配成功: frame52_from_04473_WebUAV3M_train_dropside_truck_6_0_BEST.mp4 -> frame52_from_04473_WebUAV3M_train_dropside_truck_6_0_5_results_seed1.mp4
2025-07-31 21:08:44,471 - INFO - 匹配成功: frame52_from_04561_WebUAV3M_train_ferris_wheel_pod_1_0_BEST.mp4 -> frame52_from_04561_WebUAV3M_train_ferris_wheel_pod_1_0_1_seed1.mp4
2025-07-31 21:08:44,523 - WARNING - 未找到候选文件: frame52_from_05016_WebUAV3M_train_harvester_52_0_BEST.mp4
2025-07-31 21:08:44,570 - INFO - 匹配成功: frame53_from_00063_urbanvideo_train_BEST.mp4 -> frame53_from_00063_urbanvideo_train_6_results_seed42.mp4
2025-07-31 21:08:44,624 - WARNING - 未找到候选文件: frame53_from_00117_urbanvideo_train_BEST.mp4
2025-07-31 21:08:44,677 - WARNING - 未找到候选文件: frame53_from_00402_urbanvideo_train_BEST.mp4
2025-07-31 21:08:44,730 - WARNING - 未找到候选文件: frame53_from_00426_urbanvideo_train_BEST.mp4
2025-07-31 21:08:44,783 - WARNING - 未找到候选文件: frame53_from_00510_urbanvideo_train_BEST.mp4
2025-07-31 21:08:44,835 - WARNING - 未找到候选文件: frame53_from_02969_WebUAV3M_train_bell_tower_1_0_BEST.mp4
2025-07-31 21:08:44,888 - WARNING - 未找到候选文件: frame53_from_04647_WebUAV3M_train_flyboarding_10_2_BEST.mp4
2025-07-31 21:08:44,941 - WARNING - 未找到候选文件: frame53_from_04895_WebUAV3M_train_goose_1_2_BEST.mp4
2025-07-31 21:08:44,974 - INFO - 匹配成功: frame53_from_07222_WebUAV3M_train_red_star_1_4_BEST.mp4 -> frame53_from_07222_WebUAV3M_train_red_star_1_4_4_seed1.mp4
2025-07-31 21:08:45,026 - WARNING - 未找到候选文件: frame54_from_02596_NAT2021_train_0425person1_5_0_BEST.mp4
2025-07-31 21:08:45,079 - WARNING - 未找到候选文件: frame54_from_04718_WebUAV3M_train_gaily-painted_pleasure-boat_4_10_BEST.mp4
2025-07-31 21:08:45,116 - INFO - 匹配成功: frame55_from_02941_WebUAV3M_train_beacon_26_6_BEST.mp4 -> frame55_from_02941_WebUAV3M_train_beacon_26_6_6_seed42.mp4
2025-07-31 21:08:45,171 - WARNING - 未找到候选文件: frame59_from_04745_WebUAV3M_train_gaily-painted_pleasure-boat_9_2_BEST.mp4
2025-07-31 21:08:45,234 - WARNING - 匹配失败: frame60_from_01661_NAT2021_train_0130car1_13_0_BEST.mp4
2025-07-31 21:08:45,287 - WARNING - 未找到候选文件: frame60_from_05929_WebUAV3M_train_motorboat_7_0_BEST.mp4
2025-07-31 21:08:45,340 - WARNING - 未找到候选文件: frame60_from_05949_WebUAV3M_train_ocean_liner_2_0_BEST.mp4
2025-07-31 21:08:45,393 - WARNING - 未找到候选文件: frame61_from_00754_urbanvideo_train_BEST.mp4
2025-07-31 21:08:45,422 - INFO - 匹配成功: frame61_from_01715_NAT2021_train_0131ship1_1_0_BEST.mp4 -> frame61_from_01715_NAT2021_train_0131ship1_1_0_2_seed1.mp4
2025-07-31 21:08:45,490 - WARNING - 匹配失败: frame61_from_03435_WebUAV3M_train_box_truck_4_0_BEST.mp4
2025-07-31 21:08:45,546 - WARNING - 未找到候选文件: frame61_from_04424_WebUAV3M_train_driving_1_1_BEST.mp4
2025-07-31 21:08:45,600 - WARNING - 未找到候选文件: frame61_from_04637_WebUAV3M_train_fishing_boat_2_0_BEST.mp4
2025-07-31 21:08:45,653 - WARNING - 未找到候选文件: frame61_from_06042_WebUAV3M_train_office_building_38_1_BEST.mp4
2025-07-31 21:08:45,707 - WARNING - 未找到候选文件: frame61_from_07024_WebUAV3M_train_pushing_a_cart_7_0_BEST.mp4
2025-07-31 21:08:45,761 - WARNING - 未找到候选文件: frame61_from_07274_WebUAV3M_train_riding_an_electric_bicycle_3_0_BEST.mp4
2025-07-31 21:08:45,814 - WARNING - 未找到候选文件: frame62_from_00389_urbanvideo_train_BEST.mp4
2025-07-31 21:08:45,866 - WARNING - 未找到候选文件: frame62_from_01708_NAT2021_train_0130truck2_7_0_BEST.mp4
2025-07-31 21:08:45,923 - WARNING - 匹配失败: frame62_from_02538_NAT2021_train_0425bike1_13_0_BEST.mp4
2025-07-31 21:08:45,958 - INFO - 匹配成功: frame62_from_03625_WebUAV3M_train_bulk_carrier_8_1_BEST.mp4 -> frame62_from_03625_WebUAV3M_train_bulk_carrier_8_1_8_seed1.mp4
2025-07-31 21:08:46,011 - WARNING - 未找到候选文件: frame62_from_04152_WebUAV3M_train_coach_8_1_BEST.mp4
2025-07-31 21:08:46,043 - INFO - 匹配成功: frame62_from_06050_WebUAV3M_train_office_building_40_1_BEST.mp4 -> frame62_from_06050_WebUAV3M_train_office_building_40_1_3_seed1.mp4
2025-07-31 21:08:46,097 - WARNING - 未找到候选文件: frame63_from_00031_urbanvideo_train_BEST.mp4
2025-07-31 21:08:46,151 - WARNING - 未找到候选文件: frame63_from_04513_WebUAV3M_train_elephant_8_2_BEST.mp4
2025-07-31 21:08:46,186 - INFO - 匹配成功: frame63_from_04899_WebUAV3M_train_great_wall_of_china_2_2_BEST.mp4 -> frame63_from_04899_WebUAV3M_train_great_wall_of_china_2_2_5_seed1.mp4
2025-07-31 21:08:46,255 - WARNING - 匹配失败: frame64_from_00553_urbanvideo_train_BEST.mp4
2025-07-31 21:08:46,308 - WARNING - 未找到候选文件: frame64_from_02083_NAT2021_train_0264car5_3_0_BEST.mp4
2025-07-31 21:08:46,361 - WARNING - 未找到候选文件: frame64_from_02187_NAT2021_train_0404motor1_5_0_BEST.mp4
2025-07-31 21:08:46,414 - WARNING - 未找到候选文件: frame64_from_02356_NAT2021_train_0409building1_10_0_BEST.mp4
2025-07-31 21:08:46,448 - INFO - 匹配成功: frame64_from_03814_WebUAV3M_train_carrying_26_3_BEST.mp4 -> frame64_from_03814_WebUAV3M_train_carrying_26_3_7_seed42.mp4
2025-07-31 21:08:46,502 - WARNING - 未找到候选文件: frame64_from_04450_WebUAV3M_train_dropside_truck_27_0_BEST.mp4
2025-07-31 21:08:46,556 - WARNING - 未找到候选文件: frame65_from_00213_urbanvideo_train_BEST.mp4
2025-07-31 21:08:46,609 - WARNING - 未找到候选文件: frame65_from_02020_NAT2021_train_0186building2_2_0_BEST.mp4
2025-07-31 21:08:46,663 - WARNING - 未找到候选文件: frame65_from_02039_NAT2021_train_0261building1_0_BEST.mp4
2025-07-31 21:08:46,715 - WARNING - 未找到候选文件: frame65_from_02689_NAT2021_train_0427runner4_3_0_BEST.mp4
2025-07-31 21:08:46,770 - WARNING - 未找到候选文件: frame65_from_03629_WebUAV3M_train_bulldozer_1_1_BEST.mp4
2025-07-31 21:08:46,810 - INFO - 匹配成功: frame68_from_06013_WebUAV3M_train_office_building_26_2_BEST.mp4 -> frame68_from_06013_WebUAV3M_train_office_building_26_2_8_seed1.mp4
2025-07-31 21:08:46,839 - INFO - 匹配成功: frame69_from_06159_WebUAV3M_train_office_building_82_2_BEST.mp4 -> frame69_from_06159_WebUAV3M_train_office_building_82_2_2_seed1.mp4
2025-07-31 21:08:46,892 - WARNING - 未找到候选文件: frame6_from_01546_NAT2021_train_0000car8_1_BEST.mp4
2025-07-31 21:08:46,945 - WARNING - 未找到候选文件: frame6_from_02218_NAT2021_train_0404person2_9_0_BEST.mp4
2025-07-31 21:08:46,997 - WARNING - 未找到候选文件: frame6_from_06143_WebUAV3M_train_office_building_78_1_BEST.mp4
2025-07-31 21:08:47,028 - INFO - 匹配成功: frame6_from_07272_WebUAV3M_train_riding_an_electric_bicycle_29_1_BEST.mp4 -> frame6_from_07272_WebUAV3M_train_riding_an_electric_bicycle_29_1_2_seed42.mp4
2025-07-31 21:08:47,082 - WARNING - 未找到候选文件: frame6_from_07297_WebUAV3M_train_riding_an_electric_bicycle_45_1_BEST.mp4
2025-07-31 21:08:47,135 - WARNING - 未找到候选文件: frame70_from_04006_WebUAV3M_train_chimney_5_4_BEST.mp4
2025-07-31 21:08:47,189 - WARNING - 未找到候选文件: frame70_from_04588_WebUAV3M_train_ferry_19_1_BEST.mp4
2025-07-31 21:08:47,242 - WARNING - 未找到候选文件: frame70_from_05180_WebUAV3M_train_hatchback_95_2_BEST.mp4
2025-07-31 21:08:47,296 - WARNING - 未找到候选文件: frame72_from_01522_NAT2021_train_0000car26_6_0_BEST.mp4
2025-07-31 21:08:47,327 - INFO - 匹配成功: frame72_from_02451_NAT2021_train_0421car11_8_0_BEST.mp4 -> frame72_from_02451_NAT2021_train_0421car11_8_0_8_seed42.mp4
2025-07-31 21:08:47,380 - WARNING - 未找到候选文件: frame72_from_02708_NAT2021_train_0427truck1_2_0_BEST.mp4
2025-07-31 21:08:47,433 - WARNING - 未找到候选文件: frame72_from_03557_WebUAV3M_train_bulk_carrier_24_2_BEST.mp4
2025-07-31 21:08:47,497 - WARNING - 匹配失败: frame72_from_03930_WebUAV3M_train_cell_tower_13_0_BEST.mp4
2025-07-31 21:08:47,551 - WARNING - 未找到候选文件: frame72_from_06152_WebUAV3M_train_office_building_80_1_BEST.mp4
2025-07-31 21:08:47,621 - WARNING - 匹配失败: frame73_from_00466_urbanvideo_train_BEST.mp4
2025-07-31 21:08:47,675 - WARNING - 未找到候选文件: frame73_from_01660_NAT2021_train_0130car1_12_0_BEST.mp4
2025-07-31 21:08:47,728 - WARNING - 未找到候选文件: frame73_from_02248_NAT2021_train_0408bus2_5_0_BEST.mp4
2025-07-31 21:08:47,782 - WARNING - 未找到候选文件: frame73_from_03705_WebUAV3M_train_cab_9_0_BEST.mp4
2025-07-31 21:08:47,835 - WARNING - 未找到候选文件: frame73_from_05226_WebUAV3M_train_high_speed_train_22_1_BEST.mp4
2025-07-31 21:08:47,876 - INFO - 匹配成功: frame73_from_06185_WebUAV3M_train_ox_1_0_BEST.mp4 -> frame73_from_06185_WebUAV3M_train_ox_1_0_8_seed42.mp4
2025-07-31 21:08:47,928 - WARNING - 未找到候选文件: frame73_from_07196_WebUAV3M_train_reading_3_0_BEST.mp4
2025-07-31 21:08:47,982 - WARNING - 未找到候选文件: frame74_from_00446_urbanvideo_train_BEST.mp4
2025-07-31 21:08:48,035 - WARNING - 未找到候选文件: frame74_from_02764_NAT2021_train_0428person3_5_0_BEST.mp4
2025-07-31 21:08:48,067 - INFO - 匹配成功: frame74_from_03564_WebUAV3M_train_bulk_carrier_29_1_BEST.mp4 -> frame74_from_03564_WebUAV3M_train_bulk_carrier_29_1_4_seed42.mp4
2025-07-31 21:08:48,122 - WARNING - 未找到候选文件: frame75_from_00293_urbanvideo_train_BEST.mp4
2025-07-31 21:08:48,176 - WARNING - 未找到候选文件: frame75_from_02238_NAT2021_train_0407building1_1_0_BEST.mp4
2025-07-31 21:08:48,229 - WARNING - 未找到候选文件: frame75_from_02364_NAT2021_train_0409building1_4_0_BEST.mp4
2025-07-31 21:08:48,282 - WARNING - 未找到候选文件: frame75_from_03568_WebUAV3M_train_bulk_carrier_3_1_BEST.mp4
2025-07-31 21:08:48,336 - WARNING - 未找到候选文件: frame75_from_04558_WebUAV3M_train_ferris_wheel_9_4_BEST.mp4
2025-07-31 21:08:48,389 - WARNING - 未找到候选文件: frame75_from_07171_WebUAV3M_train_reading_23_2_BEST.mp4
2025-07-31 21:08:48,443 - WARNING - 未找到候选文件: frame76_from_00787_urbanvideo_train_BEST.mp4
2025-07-31 21:08:48,497 - WARNING - 未找到候选文件: frame76_from_01856_NAT2021_train_0138person18_4_0_BEST.mp4
2025-07-31 21:08:48,529 - INFO - 匹配成功: frame76_from_04221_WebUAV3M_train_container_truck_22_2_BEST.mp4 -> frame76_from_04221_WebUAV3M_train_container_truck_22_2_1_seed1.mp4
2025-07-31 21:08:48,559 - INFO - 匹配成功: frame79_from_02006_NAT2021_train_0184car2_5_0_BEST.mp4 -> frame79_from_02006_NAT2021_train_0184car2_5_0_4_seed42.mp4
2025-07-31 21:08:48,590 - INFO - 匹配成功: frame79_from_02369_NAT2021_train_0409building1_9_0_BEST.mp4 -> frame79_from_02369_NAT2021_train_0409building1_9_0_5_seed1.mp4
2025-07-31 21:08:48,645 - WARNING - 未找到候选文件: frame79_from_03563_WebUAV3M_train_bulk_carrier_29_0_BEST.mp4
2025-07-31 21:08:48,699 - WARNING - 未找到候选文件: frame79_from_04581_WebUAV3M_train_ferry_14_1_BEST.mp4
2025-07-31 21:08:48,753 - WARNING - 未找到候选文件: frame7_from_03979_WebUAV3M_train_chimney_17_7_BEST.mp4
2025-07-31 21:08:48,807 - WARNING - 未找到候选文件: frame7_from_04515_WebUAV3M_train_elevator_1_0_BEST.mp4
2025-07-31 21:08:48,860 - WARNING - 未找到候选文件: frame80_from_00309_urbanvideo_train_BEST.mp4
2025-07-31 21:08:48,914 - WARNING - 未找到候选文件: frame80_from_01920_NAT2021_train_0139car15_2_0_BEST.mp4
2025-07-31 21:08:48,969 - WARNING - 未找到候选文件: frame80_from_06112_WebUAV3M_train_office_building_66_0_BEST.mp4
2025-07-31 21:08:48,998 - INFO - 匹配成功: frame83_from_00556_urbanvideo_train_BEST.mp4 -> frame83_from_00556_urbanvideo_train_2_seed1.mp4
2025-07-31 21:08:49,032 - INFO - 匹配成功: frame83_from_03667_WebUAV3M_train_cab_18_1_BEST.mp4 -> frame83_from_03667_WebUAV3M_train_cab_18_1_3_seed1.mp4
2025-07-31 21:08:49,032 - INFO - 处理完成。成功匹配: 138, 无匹配: 213
2025-07-31 21:08:49,034 - INFO - 结果已保存到 best.csv
