#!/usr/bin/env python3
"""
视频溯源脚本
将 best_videos 目录中的最佳视频追溯到 cluster_video 目录中的原始视频文件
"""

import os
import csv
import hashlib
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import subprocess
import json
from dataclasses import dataclass
from tqdm import tqdm

@dataclass
class VideoMatch:
    """视频匹配结果"""
    best_video_name: str
    original_video_name: str
    cluster_group: str
    match_method: str
    confidence: float
    
class VideoTracer:
    """视频溯源器"""
    
    def __init__(self, best_videos_dir: str, cluster_videos_dir: str):
        self.best_videos_dir = Path(best_videos_dir)
        self.cluster_videos_dir = Path(cluster_videos_dir)
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('video_tracing.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def get_file_md5(self, file_path: Path) -> str:
        """计算文件MD5哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            self.logger.error(f"计算MD5失败 {file_path}: {e}")
            return ""
    
    def get_file_size(self, file_path: Path) -> int:
        """获取文件大小"""
        try:
            return file_path.stat().st_size
        except Exception as e:
            self.logger.error(f"获取文件大小失败 {file_path}: {e}")
            return 0
    
    def get_video_duration(self, file_path: Path) -> float:
        """获取视频时长（秒）- 当前版本跳过，因为ffprobe不可用"""
        # ffprobe 不可用，暂时返回0
        return 0.0
    
    def extract_base_name(self, best_video_name: str) -> str:
        """从best视频名称提取基础名称（去掉_BEST后缀）"""
        if best_video_name.endswith('_BEST.mp4'):
            return best_video_name[:-9]  # 去掉 '_BEST.mp4'
        return best_video_name
    
    def find_candidate_files(self, base_name: str) -> List[Path]:
        """在cluster_video目录中查找候选文件"""
        candidates = []

        # 方法1: 精确匹配（原有逻辑）
        for file_path in self.cluster_videos_dir.glob("*.mp4"):
            if file_path.name.startswith(base_name + "_"):
                candidates.append(file_path)

        # 方法2: 如果精确匹配失败，尝试忽略帧号的匹配
        if not candidates:
            # 提取除帧号外的标识符
            parts = base_name.split('_')
            if len(parts) >= 3 and parts[0].startswith('frame'):
                # 构建不包含帧号的搜索模式
                id_part = '_'.join(parts[2:])  # 从第三部分开始（跳过frame和from）

                for file_path in self.cluster_videos_dir.glob("*.mp4"):
                    if id_part in file_path.name:
                        candidates.append(file_path)

        return candidates
    
    def match_videos(self, best_video_path: Path, candidates: List[Path]) -> Optional[VideoMatch]:
        """匹配视频文件"""
        if not candidates:
            return None
            
        best_md5 = self.get_file_md5(best_video_path)
        best_size = self.get_file_size(best_video_path)
        best_duration = self.get_video_duration(best_video_path)
        
        matches = []
        
        for candidate in candidates:
            candidate_md5 = self.get_file_md5(candidate)
            candidate_size = self.get_file_size(candidate)
            candidate_duration = self.get_video_duration(candidate)
            
            # MD5完全匹配
            if best_md5 and candidate_md5 and best_md5 == candidate_md5:
                cluster_group = self.extract_cluster_group(candidate.name)
                return VideoMatch(
                    best_video_name=best_video_path.name,
                    original_video_name=candidate.name,
                    cluster_group=cluster_group,
                    match_method="md5",
                    confidence=1.0
                )
            
            # 文件大小匹配
            if best_size > 0 and candidate_size > 0 and best_size == candidate_size:
                matches.append((candidate, "filesize", 0.9))
            
            # 视频时长匹配（允许0.1秒误差）
            if (best_duration > 0 and candidate_duration > 0 and 
                abs(best_duration - candidate_duration) <= 0.1):
                matches.append((candidate, "duration", 0.8))
        
        # 如果有匹配，选择置信度最高的
        if matches:
            best_match = max(matches, key=lambda x: x[2])
            candidate, method, confidence = best_match
            cluster_group = self.extract_cluster_group(candidate.name)
            return VideoMatch(
                best_video_name=best_video_path.name,
                original_video_name=candidate.name,
                cluster_group=cluster_group,
                match_method=method,
                confidence=confidence
            )
        
        return None
    
    def extract_cluster_group(self, cluster_video_name: str) -> str:
        """从cluster视频名称提取集群组别"""
        # 移除 .mp4 后缀
        name_without_ext = cluster_video_name[:-4]

        # 查找最后一个 _seed 的位置
        seed_pos = name_without_ext.rfind('_seed')
        if seed_pos != -1:
            name_without_seed = name_without_ext[:seed_pos]

            # 分割并查找集群编号
            parts = name_without_seed.split('_')

            # 从后往前查找数字部分作为集群组别
            for i in range(len(parts) - 1, -1, -1):
                if parts[i].isdigit():
                    return parts[i]

            # 如果没有找到数字，返回最后一个部分
            return parts[-1] if parts else "unknown"

        return "unknown"
    
    def process_all_videos(self) -> List[VideoMatch]:
        """处理所有视频文件"""
        best_videos = list(self.best_videos_dir.glob("*.mp4"))
        results = []
        no_match_count = 0
        
        self.logger.info(f"开始处理 {len(best_videos)} 个最佳视频文件")
        
        for best_video in tqdm(best_videos, desc="处理视频"):
            base_name = self.extract_base_name(best_video.name)
            candidates = self.find_candidate_files(base_name)
            
            if not candidates:
                self.logger.warning(f"未找到候选文件: {best_video.name}")
                no_match_count += 1
                # 记录无匹配的情况
                results.append(VideoMatch(
                    best_video_name=best_video.name,
                    original_video_name="NO_MATCH",
                    cluster_group="NO_CLUSTER",
                    match_method="none",
                    confidence=0.0
                ))
                continue
            
            match = self.match_videos(best_video, candidates)
            if match:
                results.append(match)
                self.logger.info(f"匹配成功: {best_video.name} -> {match.original_video_name}")
            else:
                self.logger.warning(f"匹配失败: {best_video.name}")
                no_match_count += 1
                results.append(VideoMatch(
                    best_video_name=best_video.name,
                    original_video_name="MATCH_FAILED",
                    cluster_group="UNKNOWN",
                    match_method="failed",
                    confidence=0.0
                ))
        
        self.logger.info(f"处理完成。成功匹配: {len(results) - no_match_count}, 无匹配: {no_match_count}")
        return results
    
    def save_results_to_csv(self, results: List[VideoMatch], output_file: str = "best.csv"):
        """保存结果到CSV文件"""
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['best_video_name', 'original_video_name', 'cluster_group', 'match_method', 'confidence']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for result in results:
                writer.writerow({
                    'best_video_name': result.best_video_name,
                    'original_video_name': result.original_video_name,
                    'cluster_group': result.cluster_group,
                    'match_method': result.match_method,
                    'confidence': result.confidence
                })
        
        self.logger.info(f"结果已保存到 {output_file}")

def main():
    """主函数"""
    best_videos_dir = "/data0/trz/ms-swift_SFT/video-storage/ALL/best_videos"
    cluster_videos_dir = "/data0/trz/ms-swift_SFT/video-storage/ALL/cluster_video"
    
    tracer = VideoTracer(best_videos_dir, cluster_videos_dir)
    results = tracer.process_all_videos()
    tracer.save_results_to_csv(results)
    
    # 打印统计信息
    total = len(results)
    successful = len([r for r in results if r.confidence > 0])
    print(f"\n=== 溯源统计 ===")
    print(f"总视频数: {total}")
    print(f"成功匹配: {successful}")
    print(f"匹配率: {successful/total*100:.1f}%")
    
    # 按匹配方法统计
    method_stats = {}
    for result in results:
        method = result.match_method
        method_stats[method] = method_stats.get(method, 0) + 1
    
    print(f"\n=== 匹配方法统计 ===")
    for method, count in method_stats.items():
        print(f"{method}: {count}")

if __name__ == "__main__":
    main()
